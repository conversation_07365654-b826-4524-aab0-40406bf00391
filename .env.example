PORT=4001
TZ=UTC
REQUEST_TIMEOUT=180000
#Swagger Config
SWAGGER_TITLE="APE Document Service API"
SWAGGER_DESCRIPTION="The APE Document Service API"
SWAGGER_VERSION="1.0"
# External API
EXTERNAL_API_HOST="http://localhost:4001"
EXTERNAL_API_PATH="/api/integration/external"
# Primary Database
DB_PRIMARY_TYPE="postgres"
DB_PRIMARY_HOST="localhost"
DB_PRIMARY_PORT=5432
DB_PRIMARY_USERNAME="postgres"
DB_PRIMARY_PASSWORD="Abc12345"
DB_PRIMARY_DATABASE="ape-chain-dev"
DB_PRIMARY_SYNCHRONIZE=true
DB_PRIMARY_SSL=false
DB_PRIMARY_SSL_REJECT_UNAUTHORIZED=true
# JWT HS256 config
JWT_SECRET="/q5zjNG6W0cbEdseJEySMY7xrN/5BVCK5j/CaILyRvo="
JWT_EXPIRY="100d"
JWT_REFRESH_TOKEN_SECRET="/A5zjN26W0cbEdseJEDsMY7xrN/5BVCK5j/ZolUyYbi="
JWT_REFRESH_TOKEN_EXPIRY="300d"
# Stripe Payment
STRIPE_SECRET_KEY="sk_test_51Nv11bJL111111111111111111111111111111111111111111111111111111111111111111111111111111"
STRIPE_WEBHOOK_SECRET="wh_sec_11111111111111111111111111111111111111111111111111111111111111111111111111111111111111"
STRIPE_PRODUCT_SUBSCRIPTION_ID="prod_11111111111111111111111111111111111111111111111111111111111111111111111111111111111111"
# GOOGLE AUTH
GOOGLE_CLIENT_ID=xxxxxxxxxxxxxxxxxxxxx.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=xxxxxxxxxxxxxx
GOOGLE_CALLBACK_URL=http://localhost:4001/api/client/auth/google/callback
# CHAIN WALLET 
SCAN_APIKEY=ABC
SCAN_URL=https://testnet.bscscan.com
RPC_URL=https://data-seed-prebsc-1-s2.binance.org:8545
WALLET_DEPLOYER_PRIVATEKEY=53d3...
RECORD_HASH_STORAGE_ADDRESS=******************************************


