#ConfigMap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
    name: ape-chain-api-dev-config
    namespace: ape-chain-dev
data:
    NODE_ENV: 'production'
    PORT: '80'
    TZ: 'UTC'
    REQUEST_TIMEOUT: '180000'
    #Swagger Config
    SWAGGER_TITLE: 'APE Document Service'
    SWAGGER_DESCRIPTION: 'The APE Document Service'
    SWAGGER_VERSION: '1.0'
    # Primary Database
    DB_PRIMARY_TYPE: 'postgres'
    DB_PRIMARY_HOST: 'ape-postgre.c7uzjfmteanl.ap-southeast-1.rds.amazonaws.com'
    DB_PRIMARY_PORT: '5432'
    DB_PRIMARY_USERNAME: 'ape-chain'
    DB_PRIMARY_PASSWORD: 'Ac#23pehA2in'
    DB_PRIMARY_DATABASE: 'ape-chain-dev'
    DB_PRIMARY_SYNCHRONIZE: 'true'
    DB_PRIMARY_SSL: 'true'
    DB_PRIMARY_SSL_REJECT_UNAUTHORIZED: 'false'
    #JWT HS256 config
    JWT_SECRET: '/q5zjNG6W0cbEdseJEySMY7xrN/5BVCK5j/CaILyRvo='
    JWT_EXPIRY: '100d'
    JWT_REFRESH_TOKEN_SECRET: '/A5zjN26W0cbEdseJEDsMY7xrN/5BVCK5j/ZolUyYbi='
    JWT_REFRESH_TOKEN_EXPIRY: '300d'
    # Stripe Payment
    STRIPE_SECRET_KEY: 'rk_test_51Rd0vN003Rc7nuGn9PTqBBQSfdjffWQRvN8kAXBgPxHyzmHKLxlFbjui635Pfdx0qtUv9UMq46OqPR8s61NX3Os900DLELul71'
    STRIPE_WEBHOOK_SECRET: 'whsec_zlDJpKgcjS02oQg1NSaTUI6FqwptF3hU'
    STRIPE_PRODUCT_SUBSCRIPTION_ID: 'prod_SYDXzsqAVPBTiL'
    # GOOGLE AUTH
    GOOGLE_CLIENT_ID: '228427084774-k7dfr8736kbcrn4j4ijc2s21430n46ss.apps.googleusercontent.com'
    GOOGLE_CLIENT_SECRET: '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'
    GOOGLE_CALLBACK_URL: 'https://ape-chain-api-dev.apetechs.co/api/client/auth/google/callback'
    # External API
    EXTERNAL_API_HOST: 'https://ape-chain-api-dev.apetechs.co'
    EXTERNAL_API_PATH: '/api/chain/sync-on-chain-transaction'
    EXTERNAL_API_PATH_PARTNER: '/api/partner'
    # Redirect URL Payment
    REDIRECT_URL_PAYMENT: 'https://ape-chain-client-dev.apetechs.co'
    #AWS S3 CONFIG
    LINK_UPLOAD_S3: 'ape-chain-dev'
    AWS_S3_BUCKET_NAME: 'ape-devs-co'
    AWS_S3_ACCESS_KEY_ID: '********************'
    AWS_S3_SECRET_ACCESS_KEY: '/DIKQa//iyYZUvucHau/cRItB+LCkQ76XWspfrcO'
    # CHAIN WALLET
    SCAN_APIKEY: '1WWT69YEZW1TUZ8SYSTAIYCKESZMKAR6SS'
    SCAN_URL: 'https://testnet.bscscan.com'
    RPC_URL: 'https://data-seed-prebsc-1-s2.binance.org:8545'
    WALLET_DEPLOYER_PRIVATEKEY: '53d3a9710bfcddd21a9264d8ae92f83b845e03140045988b493ce3c0c5c8377e'
    RECORD_HASH_STORAGE_ADDRESS: '******************************************'
    # GAS PRICES ETH/BNB
    BSC_RPC_URL: 'https://bsc-dataseed1.binance.org'
    ETHERSCAN_URL: 'https://api.etherscan.io/api'
    ETHERSCAN_API_KEY: '**********************************'
---
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
    name: ape-chain-api-dev
    namespace: ape-chain-dev
spec:
    replicas: 1
    selector:
        matchLabels:
            app: ape-chain-api-dev
    template:
        metadata:
            labels:
                app: ape-chain-api-dev
        spec:
            containers:
                - name: ape-chain-api-dev
                  image: 077293829360.dkr.ecr.ap-southeast-1.amazonaws.com/ape-chain-api-dev:latest
                  ports:
                      - containerPort: 80
                  envFrom:
                      - configMapRef:
                            name: ape-chain-api-dev-config
                  volumeMounts:
                      - mountPath: /etc/localtime
                        name: tz-config
            volumes:
                - name: tz-config
                  hostPath:
                      path: /usr/share/zoneinfo/Asia/Ho_Chi_Minh

---
# service.yaml
apiVersion: v1
kind: Service
metadata:
    name: ape-chain-api-dev
    namespace: ape-chain-dev
    labels:
        run: ape-chain-api-dev
spec:
    type: ClusterIP
    ports:
        - port: 80
          protocol: TCP
          targetPort: 80
    selector:
        app: ape-chain-api-dev
