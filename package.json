{"name": "happy-shop-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"start": "nest start", "dev": "nest start --watch", "debug": "nest start --debug --watch", "build": "nest build", "clean": "<PERSON><PERSON><PERSON> dist", "format": "prettier --write src/", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/cache-manager": "^2.3.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^10.4.15", "@nestjs/schedule": "^4.1.1", "@nestjs/swagger": "^7.3.1", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "8.0.1", "@nestjs/websockets": "^10.4.15", "@types/multer": "^1.4.12", "@types/redis": "^4.0.11", "aws-sdk": "^2.1692.0", "axios": "^1.7.2", "bcrypt": "^5.1.1", "cache-manager": "^4", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "class-validator-jsonschema": "^5.0.1", "cookie-parser": "^1.4.7", "dayjs": "^1.11.13", "ethers": "^6.15.0", "extensionsjs": "^1.1.9", "hot-formula-parser": "^4.0.0", "mssql": "^11.0.1", "nanoid": "3.3.4", "nestjs-i18n": "^10.5.0", "otplib": "^12.0.1", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.13.0", "qrcode": "^1.5.4", "redis": "^5.6.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "stripe": "^18.2.1", "typeorm": "0.2.34", "typeorm-transactional-cls-hooked": "^0.1.21", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.9", "@types/express": "^4.17.17", "@types/hot-formula-parser": "^4.0.4", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/passport-google-oauth20": "^2.0.16", "@types/qrcode": "^1.5.5", "@types/supertest": "^6.0.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}