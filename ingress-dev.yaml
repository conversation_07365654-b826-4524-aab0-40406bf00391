apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: nginx-ape-chain-dev-ingress-host
  namespace: ape-chain-dev
  annotations:
    kubernetes.io/ingress.class: 'nginx'
    nginx.ingress.kubernetes.io/proxy-body-size: '100m'
    nginx.ingress.kubernetes.io/proxy-read-timeout: '300'
    nginx.ingress.kubernetes.io/proxy-send-timeout: '300'
spec:
  rules:
    - host: ape-chain-api-dev.apetechs.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ape-chain-api-dev
                port:
                  number: 80
    - host: ape-chain-admin-dev.apetechs.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ape-chain-admin-dev
                port:
                  number: 80
    - host: ape-chain-client-dev.apetechs.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ape-chain-client-dev
                port:
                  number: 80                  