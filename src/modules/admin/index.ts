import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ChildModule } from '~/@core/decorator';
import { REFIX_MODULE } from '../config-module';
import { PackagePlanController } from './package/package-plan.controller';
import { PackageService } from './package/package-plan.service';
import { TermsConfigController } from './term/term.controller';
import { TermService } from './term/term.service';
import { NewsController } from './news/new-article.controller';
import { NewsService } from './news/new-article.service';
import { MemberService } from './member/member.service';
import { MemberController } from './member/member.controller';
import { AdvisoryService } from './advisory/advisory.service';
import { AdvisoryController } from './advisory/advisory.controller';
import { OrderService } from './order/order.service';
import { OrderController } from './order/order.controller';
import { PaymentService } from './payment/payment.service';
import { PaymentController } from './payment/payment.controller';
import { UploadController } from './upload/upload.controller';
import { AwsModule } from '~/@core/services/aws.module';
import { ConfigApiService } from './config-package/config-package.service';
import { ConfigApiController } from './config-package/config-package.controller';
import { DashboardService } from './dashboard/dashboard.service';
import { DashboardController } from './dashboard/dashboard.controller';
import { ApiDocContentController } from './api-doc-content/api-doc-content.controller';
import { ApiDocContentService } from './api-doc-content/api-doc-content.service';
import { DocumentApiService } from './document-api/document-api.service';
import { DocumentApiController } from './document-api/document-api.controller';
import { AdminMiddleware } from './admin.middleware';
import { RequestMethod } from '@nestjs/common';
import { AdminAuthController } from './admin-auth/admin-auth.controller';
import { AdminAuthService } from './admin-auth/admin-auth.service';

const services = [
    PackageService,
    TermService,
    NewsService,
    MemberService,
    AdvisoryService,
    OrderService,
    PaymentService,
    ConfigApiService,
    DashboardService,
    ApiDocContentService,
    DocumentApiService,
    AdminAuthService,
];

const controllers = [
    PackagePlanController,
    TermsConfigController,
    NewsController,
    MemberController,
    AdvisoryController,
    OrderController,
    PaymentController,
    UploadController,
    ConfigApiController,
    DashboardController,
    ApiDocContentController,
    DocumentApiController,
    AdminAuthController,
];

@ChildModule({
    prefix: REFIX_MODULE.admin,
    providers: services,
    controllers: controllers,
    imports: [AwsModule],
})
export class AdminModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {
        consumer
            .apply(AdminMiddleware)
            .exclude(
                { path: `${REFIX_MODULE.admin}/auth/login`, method: RequestMethod.POST },
                { path: `${REFIX_MODULE.admin}/document-api/create`, method: RequestMethod.POST },
            )
            .forRoutes({
                path: `${REFIX_MODULE.admin}/*`,
                method: RequestMethod.ALL,
            });
    }
}
