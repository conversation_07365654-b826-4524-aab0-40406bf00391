import { DefController, DefPost } from "~/@core/decorator";
import { AdminAuthService } from "./admin-auth.service";
import { Body } from "@nestjs/common";
import { AdminAuthReq } from "./dto/admin-auth.dto";


@DefController('auth')
export class AdminAuthController {
    constructor(private readonly adminAuthService: AdminAuthService) { }

    @DefPost('login')
    login(@Body() body: AdminAuthReq) {
        return this.adminAuthService.login(body);
    }
}
