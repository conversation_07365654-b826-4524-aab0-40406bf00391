import { Injectable } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import { BusinessException } from '~/@systems/exceptions';
import securityHelper from '~/@core/helpers/security.helper';
import { JwtService } from '@nestjs/jwt';
import { configEnv } from '~/@config/env';
import { UserAdminEntity } from '~/domains/primary';
import { AdminAuthReq } from './dto/admin-auth.dto';
import { UserAdminRepo } from '~/domains/primary';

@Injectable()
export class AdminAuthService {
    constructor(private jwtService: JwtService) { }

    @BindRepo(UserAdminRepo)
    private userAdminRepo: UserAdminRepo;

    private async generateRefreshToken(userId: string) {
        const { JWT_REFRESH_TOKEN_EXPIRY, JWT_REFRESH_TOKEN_SECRET } = configEnv();
        const newRefreshToken = await this.jwtService.signAsync(
            { sub: userId },
            {
                secret: JWT_REFRESH_TOKEN_SECRET,
                expiresIn: JWT_REFRESH_TOKEN_EXPIRY,
            },
        );

        return newRefreshToken;
    }

    private clearPrivateMemberData(admin: UserAdminEntity) {
        const { password, createdBy, updatedBy, createdDate, updatedDate, ...rest } = admin;
        return rest;
    }

    private async makeAuthResponse(admin: UserAdminEntity ) {
        const pipeMember = this.clearPrivateMemberData(admin);
        const payload = {
            sub: admin.id,
            ...pipeMember,
        };
        return {
            accessToken: await this.jwtService.signAsync(payload),
            refreshToken: await this.generateRefreshToken(admin.id),
            tokenType: 'Bearer',
            ...pipeMember,
        };
    }

    //login
    async login(body: AdminAuthReq) {
        const userAdmin = await this.userAdminRepo.findOne({
            where: {
                username: body.username,
            },
        });

        if (!userAdmin) {
            throw new BusinessException('admin_auth.login.error.member_not_existed');
        }

        const checkPass = await securityHelper.compare(body?.password, userAdmin.password);
        if (!checkPass) {
            throw new BusinessException('admin_auth.login.error.wrong_password');
        }
        return this.makeAuthResponse(userAdmin);
    }
}
