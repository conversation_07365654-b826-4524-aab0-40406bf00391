import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSMember } from '~/common/enums/member.enum';

export class MemberListDto extends PageRequest {
  @ApiPropertyOptional({ description: 'Email' })
  @IsOptional()
  email?: string;

  @ApiPropertyOptional({ description: 'Họ tên' })
  @IsOptional()
  fullName?: string;

  @ApiPropertyOptional({ description: 'Trạng thái' })
  @IsOptional()
  status?: NSMember.EStatus;

  //validate statú
  @ApiPropertyOptional({ description: 'Trạng thái xác thực' })
  @IsOptional()
  statusValidate?: NSMember.EStatus;
}
