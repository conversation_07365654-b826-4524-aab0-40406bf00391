
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPositive,
  IsString,
  IsInt,
  Min,
} from 'class-validator';

export class CreatePackagePlanPromotionDto {
  @ApiProperty({ description: 'ID của gói khuyến mãi' })
  @IsString()
  id?: string;

  @ApiProperty({ description: 'Giá bán cho 1 tháng (sau khi giảm)' })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  unitPrice: number;

  @ApiPropertyOptional({ description: '<PERSON><PERSON><PERSON> bán tổng cho kỳ này (sau khi giảm)' })
  @IsNumber()
  @IsOptional()
  totalPrice?: number;

  @ApiProperty({ description: 'ID của gói dịch vụ' })
  @IsString()
  packagePlanId?: string;

  @ApiProperty({ description: '<PERSON><PERSON> tháng thanh toán 1 lần (3/6/9/12)' })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  billingCycle: number;

  @ApiPropertyOptional({ description: 'Phần trăm giảm giá (%) so với giá gốc' })
  @IsOptional()
  @IsNumber()
  discountPercent?: number;

  @ApiPropertyOptional({ description: 'Ghi chú khuyến mãi (nếu có)' })
  @IsOptional()
  @IsString()
  note?: string;

  @ApiPropertyOptional({ description: 'Stripe Price ID (nếu gắn với Stripe)' })
  @IsOptional()
  @IsString()
  stripePriceId?: string;

  @ApiPropertyOptional({ description: 'Số lượng giao dịch được phép' })
  @IsOptional()
  @IsInt()
  @Min(0)
  transactionLimit?: number;

  @ApiPropertyOptional({ description: 'Số lượng cấu hình được phép' })
  @IsOptional()
  @IsInt()
  @Min(0)
  configLimit?: number;

  @ApiPropertyOptional({ description: 'Số lượng project (member-key) được phép' })
  @IsOptional()
  @IsInt()
  @Min(0)
  projectLimit?: number;

  @ApiPropertyOptional({ description: 'Số lượng byte được phép' })
  @IsOptional()
  @IsInt()
  @Min(0)
  byteLimit?: number;
}