import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsInt,
  Min,
  IsBoolean,
} from 'class-validator';
import { PageRequest } from '~/@systems/utils/page.utils';
import { NSPackage } from '~/common/enums/package.enum';
import { CreatePackagePlanPromotionDto } from './package-promotion.dto';


export class CreatePackagePlanDto {
  @ApiProperty({ description: 'Tên gói dịch vụ' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ description: 'Giá gốc' })
  @IsNumber()
  @Min(0)
  originalPrice: number;

  @ApiPropertyOptional({ description: 'Mô tả gói dịch vụ theo tháng' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: '<PERSON><PERSON> chú / miêu tả chung cho gói' })
  @IsOptional()
  @IsString()
  note?: string;

  @ApiPropertyOptional({ description: 'Là phổ biến nhất' })
  @IsOptional()
  @IsBoolean()
  isMostPopular?: boolean;

  // List Promotion
  @IsOptional()
  promotions?: CreatePackagePlanPromotionDto[];
}

export class UpdatePackagePlanDto extends CreatePackagePlanDto {
  @ApiProperty({ description: 'ID của gói dịch vụ' })
  @IsNotEmpty()
  @IsString()
  id: string;

  stripePriceYearId?: string;
  stripePriceMonthId?: string;
}

export class PackagePlanListDto extends PageRequest {
  //code
  @ApiProperty({ description: 'Mã gói dịch vụ' })
  @IsOptional()
  code?: string;

  //memberId
  @ApiProperty({ description: 'ID của thành viên' })
  @IsOptional()
  memberId?: string;

  @ApiProperty({ description: 'Tên gói dịch vụ' })
  @IsOptional()
  name?: string;

  @ApiProperty({ description: 'Giá gốc' })
  @IsOptional()
  originalPrice?: number;

  @ApiPropertyOptional({ description: 'Mô tả gói dịch vụ' })
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({ description: 'Trạng thái' })
  @IsOptional()
  status?: NSPackage.EStatus;

  @ApiPropertyOptional({ description: 'Ngày tạo' })
  @IsOptional()
  createdDateFrom?: Date;

  @ApiPropertyOptional({ description: 'Ngày tạo' })
  @IsOptional()
  createdDateTo?: Date;
}
