import { Injectable } from '@nestjs/common';
import { CreatePackagePlanDto, UpdatePackagePlanDto, PackagePlanListDto } from './dto/package.dto';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { PackagePlanRepo } from '~/domains/primary/package/package.repo';
import { NSPackage } from '~/common/enums/package.enum';
import { Between } from 'typeorm';
import { generateCodeHelper } from '~/common/helpers/generate-code.helper';
import Stripe from 'stripe';
import { configEnv } from '~/@config/env';
import { BusinessException } from '~/@systems/exceptions';
import { PackagePlanPromotionRepo } from '~/domains/primary';

@Injectable()
export class PackageService {
  private stripe: Stripe
  constructor() {
    this.stripe = new Stripe(configEnv().STRIPE_SECRET_KEY, {});
  }

  @BindRepo(PackagePlanRepo)
  private packagePlanRepo: PackagePlanRepo;

  @BindRepo(PackagePlanPromotionRepo)
  private packagePlanPromotionRepo: PackagePlanPromotionRepo;

  @DefTransaction()
  async create(dto: CreatePackagePlanDto) {
    const count = await this.packagePlanRepo.count();
    const planCode = generateCodeHelper.generateCode('APE', count + 1);
    const pkg = this.packagePlanRepo.create({
      ...dto,
      code: planCode,
    });

    // Nếu là gói dùng thử thì set giá bằng 0 hết
    const { STRIPE_PRODUCT_SUBSCRIPTION_ID } = configEnv();
    const productId = STRIPE_PRODUCT_SUBSCRIPTION_ID; // product chung cho các gói

    const newPackage = await this.packagePlanRepo.save(pkg);
    const { promotions } = dto;
    if (promotions) {
      for (const promotion of promotions) {
        if (promotion.unitPrice > 0) {
          const price = await this.stripe.prices.create({
            unit_amount: +promotion.unitPrice * 100,
            currency: "usd",
            recurring: {
              interval: 'month',
              interval_count: promotion.billingCycle, // Số lượng tháng
            },
            product: productId,
          });
          promotion.stripePriceId = price.id;
        }
        promotion.packagePlanId = newPackage.id;
        promotion.totalPrice = +promotion.unitPrice * +promotion.billingCycle;
        // Tạo promotion
        await this.packagePlanPromotionRepo.save(promotion);
      }
    }
    return newPackage;
  }

  async findAll() {
    return this.packagePlanRepo.find();
  }

  async findPagination(body: PackagePlanListDto) {
    const { code, memberId, name, status, createdDateFrom, createdDateTo, ...pageRequest } = body;
    const where: any = {};
    if (name) {
      where.name = name;
    }
    //code
    if (code) {
      where.code = code;
    }
    //memberId
    if (memberId) {
      where.memberId = memberId;
    }
    if (status) {
      where.status = status;
    }
    if (createdDateFrom && createdDateTo) {
      where.createdDate = Between(createdDateFrom, createdDateTo);
    }
    return await this.packagePlanRepo.findPagination({ where }, pageRequest);
  }

  async findOne(id: string) {
    const pkg = await this.packagePlanRepo.findOne({ where: { id } });
    if (!pkg) throw new BusinessException('package.not_found');
    const promotions = await this.packagePlanPromotionRepo.find({ where: { packagePlanId: pkg.id } });
    return {
      ...pkg,
      promotions,
    };
  }

  @DefTransaction()
  async update(body: UpdatePackagePlanDto) {
    const { id, ...dto } = body;
    const pkg = await this.findOne(id);
    if (!pkg) throw new BusinessException('package.not_found');
    const currentPromotions = await this.packagePlanPromotionRepo.find({ where: { packagePlanId: id } });

    const { STRIPE_PRODUCT_SUBSCRIPTION_ID } = configEnv();
    const productId = STRIPE_PRODUCT_SUBSCRIPTION_ID; // product chung cho các gói

    if(dto.promotions) {
      for (const promotion of dto.promotions) {
        if (promotion.unitPrice > 0) {
          const currentPrice = currentPromotions.find((p) => p.billingCycle === promotion.billingCycle && p.id == promotion.id);
          if (currentPrice && currentPrice.stripePriceId && currentPrice.totalPrice !== promotion.totalPrice) {
            await this.stripe.prices.update(currentPrice.stripePriceId, {
              active: false,
            });
          }
          const price = await this.stripe.prices.create({
            unit_amount: +promotion.totalPrice * 100,
            currency: "usd",
            recurring: {
              interval: 'month',
              interval_count: promotion.billingCycle, // Số lượng tháng
            },
            product: productId,
          });
          promotion.stripePriceId = price.id;
          promotion.totalPrice = +promotion.unitPrice * +promotion.billingCycle;
        }
        // Tạo promotion
        await this.packagePlanPromotionRepo.save(promotion);
      }
    }

    delete dto.promotions
    return this.packagePlanRepo.update(
      {
        id,
      },
      {
        ...dto,
      },
    );
  }

  @DefTransaction()
  async inActive(id: string) {
    const pkg = await this.findOne(id);
    if (!pkg) throw new BusinessException('package.not_found');
    return await this.packagePlanRepo.update(
      {
        id,
      },
      {
        status: NSPackage.EStatus.INACTIVE,
      },
    );
  }

  @DefTransaction()
  async active(id: string) {
    const pkg = await this.findOne(id);
    if (!pkg) throw new BusinessException('package.not_found');
    return await this.packagePlanRepo.update(
      {
        id,
      },
      {
        status: NSPackage.EStatus.ACTIVE,
      },
    );
  }
}
