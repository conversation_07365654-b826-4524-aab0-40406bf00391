import { Injectable } from '@nestjs/common';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { ApiDocContentRepo } from '~/domains/primary/api-doc-content/api-doc.content.repo';
import {
    ApiDocContentCreateDto,
    ApiDocContentUpdateDto,
    ApiDocContentListDto,
} from './dto/api-doc-content.dto';
import { BusinessException } from '~/@systems/exceptions';

@Injectable()
export class ApiDocContentService {
    constructor() {}

    @BindRepo(ApiDocContentRepo)
    private apiDocContentRepo: ApiDocContentRepo;

    async findAll(query: ApiDocContentListDto) {
        const { slug, title, ...pageRequest } = query;
        const where: any = {};
        if (slug) {
            where.slug = slug;
        }
        if (title) {
            where.title = title;
        }

        const result = this.apiDocContentRepo.findPagination(
            { ...where, order: { sort: 'ASC' } },
            pageRequest,
        );

        return result;
    }

    async findOne(id: string) {
        const result = this.apiDocContentRepo.findOne({ where: { id } });
        return result;
    }

    @DefTransaction()
    async create(dto: ApiDocContentCreateDto) {
        const created = this.apiDocContentRepo.create(dto);
        return this.apiDocContentRepo.save(created);
    }

    @DefTransaction()
    async update(dto: ApiDocContentUpdateDto) {
        const { id, ...updateDto } = dto;
        const existing = await this.apiDocContentRepo.findOne({ id });
        if (!existing) throw new BusinessException('API Doc Content not found');
        return this.apiDocContentRepo.update({ id }, { ...updateDto, updatedDate: new Date() });
    }
}
