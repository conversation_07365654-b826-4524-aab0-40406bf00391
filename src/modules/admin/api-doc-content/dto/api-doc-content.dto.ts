
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional } from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSConfig } from '~/common/enums';

export class ApiDocContentListDto extends PageRequest {
    @ApiPropertyOptional({ description: 'Slug URL' })
    @IsString()
    @IsOptional()
    slug?: string;

    @ApiPropertyOptional({ description: 'Tiêu đề' })
    @IsString()
    @IsOptional()
    title?: string;
}

// Create DTO
export class ApiDocContentCreateDto {
    @ApiPropertyOptional({ description: 'Slug URL' })
    slug?: string;

    @ApiProperty({ description: 'Tiêu đề' })
    @IsNotEmpty()
    @IsString()
    title: string;

    @ApiProperty({ description: 'Nội dung dạng HTML' })
    @IsNotEmpty()
    @IsString()
    content: string;

    @ApiPropertyOptional({ description: 'Ngôn ngữ', enum: NSConfig.EApiLDocumentLang })
    @IsOptional()
    locale?: NSConfig.EApiLDocumentLang;

    @ApiPropertyOptional({ description: 'Thứ tự hiển thị' })
    @IsOptional()
    sort?: number;
}

// Update
export class ApiDocContentUpdateDto extends ApiDocContentCreateDto {
    @ApiProperty({ description: 'ID của API Doc Content' })
    @IsNotEmpty()
    @IsString()
    id: string;
}
    