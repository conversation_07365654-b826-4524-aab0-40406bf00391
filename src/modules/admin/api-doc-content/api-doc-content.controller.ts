import { Body, Param, Query } from "@nestjs/common";
import { DefController, DefGet, DefPost } from "~/@core/decorator";
import { ApiDocContentService } from "./api-doc-content.service";
import { ApiDocContentCreateDto, ApiDocContentUpdateDto, ApiDocContentListDto } from "./dto/api-doc-content.dto";

@DefController('api-doc-content')
export class ApiDocContentController {
    constructor(private readonly service: ApiDocContentService) { }

    @DefGet('list', {
        summary: 'Lấy danh sách API Doc Content',
    })
    findAll(@Query() query: ApiDocContentListDto) {
        return this.service.findAll(query);
    }

    @DefGet(':id', {
        summary: 'Lấy thông tin chi tiết API Doc Content',
    })
    findOne(@Param('id') id: string) {
        return this.service.findOne(id);
    }

    @DefPost('create', {
        summary: 'Tạo mới một API Doc Content',
    })
    create(@Body() dto: ApiDocContentCreateDto) {
        return this.service.create(dto);
    }

    @DefPost('update', {
        summary: 'Cập nhật API Doc Content',
    })
    update(@Body() dto: ApiDocContentUpdateDto) {
        return this.service.update(dto);
    }
}
