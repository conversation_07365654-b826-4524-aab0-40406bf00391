// src/terms-config/terms-config.service.ts
import { Injectable } from '@nestjs/common';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { TermRepo } from '~/domains/primary/term/term.repo';
import { CreateTermsConfigDto, UpdateTermsConfigDto, TermListDto } from './dto/term.dto';
import { NSTerm } from '~/common/enums/term.enum';
import { ILike } from 'typeorm';
import { BusinessException } from '~/@systems/exceptions';

@Injectable()
export class TermService {
  constructor() {}

  @BindRepo(TermRepo)
  private termRepo: TermRepo;

  findAll() {
    return this.termRepo.find();
  }

  findPagination(body: TermListDto) {
    const { title, status, ...pageRequest } = body;
    const where: any = {};
    if (title) {
      where.title = ILike(`%${title}%`);
    }
    if (status) {
      where.status = status;
    }
    return this.termRepo.findPagination({ where }, pageRequest);
  }

  findOne(id: string) {
    return this.termRepo.findOne({ id });
  }

  @DefTransaction()
  async create(dto: CreateTermsConfigDto) {
    const created = this.termRepo.create(dto);
    return this.termRepo.save(created);
  }

  @DefTransaction()
  async update(dto: UpdateTermsConfigDto) {
    const { id, ...updateDto } = dto;
    const existing = await this.termRepo.findOne({ id });
    if (!existing) throw new BusinessException('term.not_found');
    return this.termRepo.update({ id }, { ...updateDto, updatedDate: new Date() });
  }

  @DefTransaction()
  async inActive(body: any) {
    const { id } = body;
    const existing = await this.termRepo.findOne({ id });
    if (!existing) throw new BusinessException('term.not_found');
    return this.termRepo.update({ id }, { status: NSTerm.EStatus.INACTIVE });
  }

  @DefTransaction()
  async active(body: any) {
    const { id } = body;
    const existing = await this.termRepo.findOne({ id });
    if (!existing) throw new BusinessException('term.not_found');
    return this.termRepo.update({ id }, { status: NSTerm.EStatus.ACTIVE });
  }
}
