// src/terms-config/terms-config.controller.ts
import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { TermService } from './term.service';
import { ApiTags } from '@nestjs/swagger';
import { DefController, DefGet, DefPost } from '~/@core/decorator';
import { CreateTermsConfigDto, UpdateTermsConfigDto, TermListDto } from './dto/term.dto';

@ApiTags('Terms Config')
@DefController('term')
export class TermsConfigController {
  constructor(private readonly termService: TermService) {}

  @DefGet('', {
    summary: '<PERSON><PERSON><PERSON> danh sách cấu hình điều khoản',
  })
  findAll() {
    return this.termService.findAll();
  }

  @DefPost('pagination', {
    summary: '<PERSON><PERSON><PERSON> danh sách cấu hình điều khoản',
  })
  findPagination(@Body() body: TermListDto) {
    return this.termService.findPagination(body);
  }

  @DefPost('', {
    summary: 'Tạo mới một cấu hình điều khoản',
  })
  create(@Body() dto: CreateTermsConfigDto) {
    return this.termService.create(dto);
  }

  @DefPost('update', {
    summary: 'Cập nhật cấu hình điều khoản',
  })
  update(@Body() dto: UpdateTermsConfigDto) {
    return this.termService.update(dto);
  }

  @DefPost('inactive', {
    summary: 'Đổi trạng thái cấu hình điều khoản về Inactive',
  })
  inActive(@Body() body: any) {
    return this.termService.inActive(body);
  }

  @DefPost('active', {
    summary: 'Đổi trạng thái cấu hình điều khoản về Active',
  })
  active(@Body() body: any) {
    return this.termService.active(body);
  }
}
