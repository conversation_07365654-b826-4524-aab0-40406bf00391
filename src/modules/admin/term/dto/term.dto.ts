// src/terms-config/dto/create-terms-config.dto.ts
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsBoolean, IsOptional } from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSTerm } from '~/common/enums/term.enum';

export class TermListDto extends PageRequest {
  @ApiProperty({ description: 'Tên cấu hình điều khoản' })
  @IsOptional()
  title?: string;

  @ApiPropertyOptional({ description: 'Trạng thái' })
  @IsOptional()
  status?: NSTerm.EStatus;
}

export class CreateTermsConfigDto {
  @ApiProperty({ description: 'Tên cấu hình điều khoản' })
  @IsNotEmpty()
  @IsString()
  title: string;

  @ApiProperty({ description: 'Nội dung điều khoản dạng HTML' })
  @IsNotEmpty()
  @IsString()
  contentHtml: string;

  @ApiPropertyOptional({ description: 'Trạng thái' })
  @IsOptional()
  status?: NSTerm.EStatus; // Default là Active
}

export class UpdateTermsConfigDto extends CreateTermsConfigDto {
  @IsNotEmpty()
  @IsString()
  id: string;
}
