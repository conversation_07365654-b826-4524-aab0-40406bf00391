import { Injectable } from '@nestjs/common';
import {
  ConfigLogDto,
  ConfigApiListDto
} from './dto/config-package.dto';
import {
  ConfigApiRepo,
  ConfigApiDetailRepo,
} from '~/domains/primary/config-api/config-api.repo';
import { BindRepo } from '~/@core/decorator';
import { NSConfig } from '~/common/enums/config.enum';
import { MemberApiRepo } from '~/domains/primary/member-api/member-api.repo';
import * as _ from 'lodash';
import { MemberApiLogRepo } from '~/domains/primary/member-api-log/member-api-log.repo';
import { memberSessionContext } from '~/modules/client/member-session.context';
import { BusinessException } from '~/@systems/exceptions';

@Injectable()
export class ConfigApiService {
  constructor() {}

  @BindRepo(ConfigApiRepo)
  private configRepo: ConfigApiRepo;

  @BindRepo(ConfigApiDetailRepo)
  private detailRepo: ConfigApiDetailRepo;

  @BindRepo(MemberApiRepo)
  private memberApiRepo: MemberApiRepo;

  @BindRepo(MemberApiLogRepo)
  private memberApiLogRepo: MemberApiLogRepo;

  async findPagination(body: ConfigApiListDto) {
    const { memberId } = memberSessionContext;
    const { code, name, status, ...pageRequest } = body;
    const where: any = { memberId };
    if (code) {
      where.code = code;
    }
    if (name) {
      where.name = name;
    }
    if (status) {
      where.status = status;
    }
    return this.configRepo.findPagination({ where }, pageRequest);
  }

  async findOne(id: string) {
    const config = await this.configRepo.findOne(id);
    if (!config) throw new BusinessException('config_package.not_found'); 
    const details = await this.detailRepo.find({ where: { configApiId: config.id } });

    const apiInfo = await this.memberApiRepo.findOne({ where: { configApiId: config.id } });
    return {
      ...config,
      apiInfo,
      fields: details || [],
    };
  }

  async findLogs(body: ConfigLogDto) {
    const { configApiId, ...pageRequest } = body;
    return this.memberApiLogRepo.findPagination(
      { where: { configApiId: body.configApiId } },
      pageRequest,
    );
  }
}
