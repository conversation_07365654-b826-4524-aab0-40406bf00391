import { <PERSON>, Param, Res } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { ConfigApiService } from './config-package.service';
import {
  ConfigLogDto,
  ConfigApiListDto,
} from './dto/config-package.dto';
import { DefController, DefPost } from '~/@core/decorator';

@ApiTags('Config Package API')
@DefController('config-api')
export class ConfigApiController {
  constructor(private readonly service: ConfigApiService) {}

  @DefPost('pagination', {
    summary: 'L<PERSON>y danh sách cấu hình',
  })
  findPagination(@Body() body: ConfigApiListDto) {
    return this.service.findPagination(body);
  }

  @DefPost('logs', {
    summary: 'Lấy danh sách log của cấu hình',
  })
  findLogs(@Body() body: ConfigLogDto) {
    return this.service.findLogs(body);
  }

  @DefPost('detail', {
    summary: '<PERSON><PERSON><PERSON> thông tin chi tiết cấu hình',
  })
  findOne(@Body('id') id: string) {
    return this.service.findOne(id);
  }
}
