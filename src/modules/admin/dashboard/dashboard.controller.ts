import { DefController, DefGet } from '~/@core/decorator';
import { DashboardService } from './dashboard.service';
import { ParseIntPipe, Query } from '@nestjs/common';

@DefController('dashboard')
export class DashboardController {
  constructor(private readonly service: DashboardService) {}

  @DefGet('total-customer', {
    summary: 'Lấy tổng số lượng khách hàng',
  })
  getTotalCustomer() {
    return this.service.getTotalCustomer();
  }

  @DefGet('total-order', {
    summary: 'Lấy tổng số lượng đơn hàng',
  })
  getTotalOrder() {
    return this.service.getTotalOrder();
  }

  @DefGet('total-revenue', {
    summary: 'L<PERSON>y tổng doanh thu của tất cả các đơn hàng đã thanh toán',
  })
  getTotalRevenue() {
    return this.service.getTotalRevenue();
  }

  @DefGet('customer-stats', {
    summary: 'L<PERSON>y dữ liệu biểu đồ thống kê khách hàng',
  })
  getCustomerStats(@Query('year') year: number, @Query('month') month?: number) {
    return this.service.getCustomerStats(year, month);
  }

  @DefGet('order-stats', {
    summary: 'Lấy dữ liệu biểu đồ thống kê đơn hàng',
  })
  getOrderStats(@Query('year') year: number, @Query('month') month?: number) {
    return this.service.getOrderStats(year, month);
  }

  @DefGet('package-statistics', {
    summary: 'Lấy dữ liệu biểu đồ thống kê gói dịch vụ',
  })
  getPackageStatisticsChart(@Query('year') year: number) {
    return this.service.getPackageStatisticsChart(year);
  }

  @DefGet('payment-gateway-stats', {
    summary: 'Lấy dữ liệu biểu đồ thống kê phương thức thanh toán',
  })
  getPaymentGatewayStats(@Query('year') year: number) {
    return this.service.getPaymentGatewayStats(year);
  }

  @DefGet('revenue-stats', {
    summary: 'Lấy dữ liệu biểu đồ thống kê doanh thu mua/gia hạn gói dịch vụ',
  })
  getRevenueStats(@Query('year') year: number, @Query('month') month?: number) {
    return this.service.getRevenueStats(year, month);
  }

  /**
   * Doanh thu của từng gói theo tổng doanh thu, bao gồm giá trị tiền và %
   * Tiền là chart Line, % là chart Pie
   * @param year 
   * @param month 
   * @returns 
   * order -> order-items -> package + payment-transaction -> orderId
   */
  @DefGet('revenue-by-package', {
    summary: 'Lấy dữ liệu biểu đồ thống kê doanh thu theo gói dịch vụ',
  })
  getRevenueByPackage(@Query('year') year: number, @Query('month') month?: number) {
    return this.service.getRevenueByPackage(year, month);
  }

  @DefGet('transaction-stats', {
    summary: 'Lấy dữ liệu biểu đồ thống kê số lượng giao dịch mua/gia hạn gói dịch vụ',
  })
  getTransactionStats(@Query('year') year: number) {
    return this.service.getTransactionStats(year);
  }

  @DefGet('transaction-stats-by-month', {
    summary: 'Lấy dữ liệu biểu đồ thống kê số lượng giao dịch mua/gia hạn gói dịch vụ theo tháng',
  })
  getTransactionStatsByMonth(@Query('month') month: number) {
    return this.service.getTransactionStatsByMonth(month);
  }

  @DefGet('onchain-transaction-stats', {
    summary: 'Lấy dữ liệu biểu đồ thống kê số lượng giao dịch onchain',
  })
  getOnChainTransactionStats(@Query('year') year: number) {
    return this.service.getOnChainTransactionStats(year);
  }

  @DefGet('gas-stats', {
    summary: 'Lấy dữ liệu biểu đồ thống kê chi phí gas onchain',
  })
  getGasStats(@Query('year') year: number) {
    return this.service.getGasStats(year);
  }
}
