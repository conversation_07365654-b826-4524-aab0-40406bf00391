// src/news/news.service.ts
import { Injectable } from '@nestjs/common';
import { generateCodeHelper } from '~/common/helpers/generate-code.helper';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { MediaRepo } from '~/domains/primary/media/media.repo';
import { NewsArticleRepo } from '~/domains/primary/news/new-article.repo';
import {
  CreateNewsArticleDto,
  NewsArticleListDto,
  UpdateNewsArticleDto,
} from './dto/new-article.dto';
import { NSNews } from '~/common/enums/news.enum';
import { Between, ILike, Like } from 'typeorm';
import { isUuid } from '~/common/helpers/validate.helper';
import { BusinessException } from '~/@systems/exceptions';

@Injectable()
export class NewsService {
  constructor() {}

  @BindRepo(NewsArticleRepo)
  private newsRepo: NewsArticleRepo;

  @BindRepo(MediaRepo)
  private mediaRepo: MediaRepo;

  @DefTransaction()
  async create(data: CreateNewsArticleDto) {
    const slug = generateCodeHelper.slugify(data.title);
    const article = this.newsRepo.create({ ...data, slug });
    const saved = await this.newsRepo.save(article);

    if (data.media && data.media.length > 0) {
      const mediaList = data.media.map(m =>
        this.mediaRepo.create({
          url: m.url,
          refId: saved.id,
          refTable: 'news_article',
        }),
      );
      await this.mediaRepo.save(mediaList);
    }
    return saved;
  }

  async findAll(params: NewsArticleListDto) {
    const {
      title,
      slug,
      status,
      publishedAtFrom,
      publishedAtTo,
      createdBy,
      createdDateFrom,
      createdDateTo,
      ...pageRequest
    } = params;
    const where: any = {};
    if (title) {
      where.title = ILike(`%${title}%`);
    }
    if (slug) {
      where.slug = slug;
    }
    if (status) {
      where.status = status;
    }
    if (publishedAtFrom && publishedAtTo) {
      where.publishedAt = Between(publishedAtFrom, publishedAtTo);
    }
    if (createdBy) {
      where.createdBy = createdBy;
    }
    if (createdDateFrom && createdDateTo) {
      where.createdDate = Between(createdDateFrom, createdDateTo);
    }
    return this.newsRepo.findPagination({ where }, pageRequest);
  }

  async findOne(param: string) {
    let article;
    if (isUuid(param)) {
      // Truy vấn theo UUID
      article = await this.newsRepo.findOne({ where: { id: param } });
    } else {
      // Truy vấn theo slug
      article = await this.newsRepo.findOne({ where: { slug: param } });
    }

    if (!article) throw new BusinessException('News not found');

    const images = await this.mediaRepo.find({
      where: { refId: article.id, refTable: 'news_article' },
    });

    return { article, images };
  }

  @DefTransaction()
  async update(updateDto: UpdateNewsArticleDto) {
    const { id, ...dto } = updateDto;
    const existing = await this.newsRepo.findOne({ id });
    if (!existing) throw new BusinessException('News not found');

    const slug = updateDto.title ? generateCodeHelper.slugify(updateDto.title) : existing.slug;
    const updated = { ...this.newsRepo.merge(existing, dto, { slug }), updatedDate: new Date() };
    const saved = await this.newsRepo.update({ id }, updated);

    if (updateDto.media) {
      await this.mediaRepo.delete({ refId: id, refTable: 'news_article' });

      const newMedia = updateDto.media.map(m =>
        this.mediaRepo.create({
          url: m.url,
          refId: id,
          refTable: 'news_article',
        }),
      );
      await this.mediaRepo.save(newMedia);
    }

    return saved;
  }

  @DefTransaction()
  async inActive(id: string) {
    const article = await this.newsRepo.findOne({ id });
    if (!article) throw new BusinessException('News not found');
    return await this.newsRepo.update(
      {
        id,
      },
      {
        status: NSNews.EStatus.INACTIVE,
        createdDate: new Date(),
      },
    );
  }

  @DefTransaction()
  async active(id: string) {
    const article = await this.newsRepo.findOne({ id });
    if (!article) throw new BusinessException('News not found');
    return await this.newsRepo.update(
      {
        id,
      },
      {
        status: NSNews.EStatus.PUBLISHED,
      },
    );
  }
}
