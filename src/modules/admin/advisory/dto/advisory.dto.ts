import { IsEmail, <PERSON>E<PERSON>, IsOptional, IsString, IsUUID, Length } from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSAdvisory } from '~/common/enums/advisory.enum';

export class CreateAdvisoryDto {
    @IsString()
    @Length(1, 150)
    fullName: string;

    @IsOptional()
    @IsEmail()
    email?: string;

    @IsOptional()
    @IsString()
    phone?: string;

    @IsOptional()
    @IsString()
    subject?: string;

    @IsOptional()
    @IsString()
    message?: string;

    @IsOptional()
    @IsUUID()
    customerId?: string;
    
    @IsOptional()
    @IsEnum(NSAdvisory.EStatus)
    status?: NSAdvisory.EStatus;
}

export class UpdateAdvisoryDto extends CreateAdvisoryDto {
    @IsString()
    @IsUUID()
    id: string;
}

export class AdvisoryListDto extends PageRequest {
    @IsOptional()
    @IsString()
    fullName?: string;

    @IsOptional()
    @IsString()
    email?: string;

    @IsOptional()
    @IsString()
    phone?: string;

    @IsOptional()
    @IsString()
    subject?: string;

    @IsOptional()
    @IsEnum(NSAdvisory.EStatus)
    status?: NSAdvisory.EStatus;

    @IsOptional()
    @IsUUID()
    customerId?: string;

    //createdDateFrom
    @IsOptional()
    createdDateFrom?: Date;

    //createdDateTo
    @IsOptional()
    createdDateTo?: Date;
}
