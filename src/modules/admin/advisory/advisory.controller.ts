import { Body, Param } from '@nestjs/common';
import { AdvisoryService } from './advisory.service';
import { CreateAdvisoryDto, UpdateAdvisoryDto, AdvisoryListDto } from './dto/advisory.dto';
import { DefController, DefGet, DefPost } from '~/@core/decorator';
import { NSAdvisory } from '~/common/enums';

@DefController('advisory')
export class AdvisoryController {
  constructor(private readonly advisoryService: AdvisoryService) {}

  @DefPost('', {
    summary: 'Tạo mới một vấn đề tư vấn',
  })
  create(@Body() dto: CreateAdvisoryDto) {
    return this.advisoryService.create(dto);
  }

  @DefPost('pagination', {
    summary: 'L<PERSON>y danh sách vấn đề tư vấn',
  })
  findAll(@Body() params: AdvisoryListDto) {
    return this.advisoryService.findAll(params);
  }

  @DefGet('detail/:id', {
    summary: '<PERSON><PERSON><PERSON> thông tin chi tiết vấn đề tư vấn',
  })
  findOne(@Param('id') id: string) {
    return this.advisoryService.findOne(id);
  }

  @DefPost('update', {
    summary: 'Cập nhật trạng thái vấn đề tư vấn',
  })
  update(@Body() body: { id: string; status: NSAdvisory.EStatus }) {
    return this.advisoryService.updateStatus(body);
  }
}
