import { Body } from '@nestjs/common';
import { DefController, DefPost } from '~/@core/decorator';
import { OrderService } from './order.service';
import { CreateOrderDto, OrderListDto, UpdatePriceDto } from './dto/order.dto';

@DefController('order')
export class OrderController {
  constructor(private readonly orderService: OrderService) {}

  @DefPost('create', {
    summary: 'Tạo mới một đơn hàng',
  })
  create(@Body() dto: CreateOrderDto) {
    return this.orderService.create(dto);
  }

  @DefPost('detail', {
    summary: '<PERSON><PERSON>y thông tin chi tiết đơn hàng',
  })
  findOne(@Body('id') id: string) {
    return this.orderService.findOne(id);
  }

  @DefPost('pagination', {
    summary: 'L<PERSON>y danh sách đơn hàng của 1 member',
  })
  findPagination(@Body() body: OrderListDto) {
    return this.orderService.findPagination(body);
  }

  @DefPost('update-price', {
    summary: '<PERSON><PERSON><PERSON> danh sách đơn hàng của 1 member',
  })
  updatePrice(@Body() body: UpdatePriceDto) {
    return this.orderService.updatePrice(body);
  }
}
