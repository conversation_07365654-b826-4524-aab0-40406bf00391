import { Injectable } from '@nestjs/common';
import { NSPayment } from '~/common/enums/payment.enum';
import { CreateOrderDto, UpdatePriceDto } from './dto/order.dto';
import { BindRepo } from '~/@core/decorator';
import {
  MemberPackageRepo,
  OrderItemRepo,
  PackagePlanRepo,
  PaymentTransactionRepo,
} from '~/domains/primary';
import { OrderRepo } from '~/domains/primary';
import { DefTransaction } from '~/@core/decorator';
import { OrderListDto } from './dto/order.dto';
import { Between, ILike, In } from 'typeorm';
import { MemberRepo } from '~/domains/primary';
import { NSOrder } from '~/common/enums/order.enum';
import { memberSessionContext } from '../member-session.context';
import Stripe from 'stripe';
import { configEnv } from '~/@config/env';
import { BusinessException } from '~/@systems/exceptions';
import { generateCodeHelper } from '~/common/helpers/generate-code.helper';
import { PackagePlanPromotionRepo } from '~/domains/primary/package-plan-promotion/package-plan-promotion.repo';

@Injectable()
export class OrderService {
  private stripe: Stripe;
  constructor() {
    this.stripe = new Stripe(configEnv().STRIPE_SECRET_KEY, {});
  }

  @BindRepo(OrderRepo)
  private orderRepo: OrderRepo;

  @BindRepo(OrderItemRepo)
  private orderItemRepo: OrderItemRepo;

  @BindRepo(MemberRepo)
  private memberRepo: MemberRepo;

  @BindRepo(PackagePlanRepo)
  private packagePlanRepo: PackagePlanRepo;

  @BindRepo(PaymentTransactionRepo)
  private paymentTransactionRepo: PaymentTransactionRepo;

  @BindRepo(MemberPackageRepo)
  private memberPackageRepo: MemberPackageRepo;

  @BindRepo(PackagePlanPromotionRepo)
  private packagePlanPromotionRepo: PackagePlanPromotionRepo;

  @DefTransaction()
  async create(dto: CreateOrderDto) {
    const { memberId } = memberSessionContext;
    // Kiểm tra thông tin memberId
    const member = await this.memberRepo.findOne({ where: { id: memberId } });
    if (!member) throw new BusinessException('member.member_not_found');

    const planIds = dto.plans.map(plan => plan.packagePlanId);
    const plans = await this.packagePlanRepo.find({ where: { id: In(planIds) } });
    if (!plans || plans.length !== planIds.length) {
      throw new BusinessException('plan.package_not_found');
    }

    if (plans.some(plan => plan.originalPrice === 0)) {
      if (member.isTrialUsed) throw new BusinessException('order.trial_package_used');
      await this.memberRepo.update({ id: memberId }, { isTrialUsed: true });
    }

    const promotion = await this.packagePlanPromotionRepo.findOne({
      where: { packagePlanId: plans[0].id, billingCycle: 1 },
    });

    const orderCode = generateCodeHelper.generateOrderCode();
    const totalPrice = dto.plans.reduce((acc, plan) => acc + +promotion.unitPrice, 0);
    const totalPriceVat = +totalPrice + (+totalPrice * 0.1);
    const vat = +totalPriceVat - +totalPrice;

    const newOrders = this.orderRepo.create({
      ...dto,
      memberId,
      orderCode,
      totalPrice,
      totalPriceVat,
      vat,
      status: NSOrder.EStatus.DRAFT,
      paymentStatus: NSOrder.EPaymentStatus.UNPAID,
    });
    await this.orderRepo.save(newOrders);
    const newOrderItems = dto.plans.map(plan =>
      this.orderItemRepo.create({
        ...plan,
        orderId: newOrders.id,
        price: promotion.unitPrice,
      }),
    );
    await this.orderItemRepo.save(newOrderItems);
    return {
      order: newOrders,
      isTrial: false,
      memberPackage: null,
    };
  }

  async findAll() {
    return this.orderRepo.find();
  }

  async findPagination(body: OrderListDto) {
    const { memberId } = memberSessionContext;
    const {
      status,
      paymentStatus,
      paymentDateFrom,
      paymentDateTo,
      packagePlanId,
      packageName,
      ...pageRequest
    } = body;
    const where: any = { memberId };
    where.status = In([NSOrder.EStatus.COMPLETED, NSOrder.EStatus.PENDING]);
    if (status) {
      where.status = status;
    }
    if (paymentStatus) {
      where.paymentStatus = paymentStatus;
    }

    if (paymentDateFrom && paymentDateTo) {
      where.paymentDate = Between(paymentDateFrom, paymentDateTo);
    }
    if (packagePlanId) {
      where.packagePlanId = packagePlanId;
    }
    const { data, total } = await this.orderRepo.findPagination(
      { where, order: { createdDate: 'DESC' } },
      pageRequest,
    );

    if (packageName) {
      const packagePlan = await this.packagePlanRepo.find({ where: { name: ILike(packageName) } });
      if (!packagePlan) throw new BusinessException('order.package_not_found');
      where.packagePlanId = In(packagePlan.map(p => p.id));
    }

    const packages = await this.packagePlanRepo.find();

    if (data.length > 0) {
      const mapping = data.map(async item => {
        const items = await this.orderItemRepo.find({ where: { orderId: item.id } });
        const packagePlans = items.map(i => packages.find(p => p.id === i.packagePlanId));
        return {
          ...item,
          items: items.map(i => ({
            ...i,
            packagePlan: packagePlans.find(p => p.id === i.packagePlanId),
          })),
        };
      });
      return {
        data: await Promise.all(mapping),
        total,
      };
    }
    return {
      data: [],
      total: 0,
    };
  }

  async findOne(id: string) {
    const order = await this.orderRepo.findOne({ where: { id } });
    if (!order) throw new BusinessException('order.not_found');
    const memberPackage = await this.memberPackageRepo.findOne({ where: { orderId: order.id } });
    const packages = await this.packagePlanRepo.findOne({
      where: { id: memberPackage.packagePlanId },
    });

    // payment-transaction mapping
    const paymentTransactions = await this.paymentTransactionRepo.find({
      where: { orderId: order.id },
    });

    // Chi tiết hóa đơn trên Stripe
    let invoice = null;
    if (order.invoiceId && order.invoiceProvider === NSPayment.EPaymentProvider.STRIPE) {
      invoice = await this.stripe.invoices.retrieve(order.invoiceId);
    }

    return {
      ...order,
      item: {
        ...memberPackage,
        packageName: packages.name,
        packageCode: packages.code,
      },
      paymentTransactions,
      invoice: {
        id: invoice.id,
        status: invoice.status,
        amount_due: invoice.amount_due,
        amount_paid: invoice.amount_paid,
        customer_email: invoice.customer_email,
        hosted_invoice_url: invoice.hosted_invoice_url,
        invoice_pdf: invoice.invoice_pdf, // 🔗 Link tải PDF
        created: invoice.created,
        due_date: invoice.due_date,
      },
    };
  }

  async updatePrice(body: UpdatePriceDto) {
    const { id, totalPrice } = body;
    const orders = await this.orderRepo.findOne({ where: { id } });
    if (!orders) throw new BusinessException('payment.order_not_found');
    return await this.orderRepo.update({ id }, { totalPrice });
  }
}
