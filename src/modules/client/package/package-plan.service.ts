import { Injectable, } from '@nestjs/common';
import { PackagePlanListDto } from './dto/package.dto';
import { BindRepo, } from '~/@core/decorator';
import { PackagePlanRepo } from '~/domains/primary/package/package.repo';
import { NSPackage } from '~/common/enums/package.enum';
import { PackagePlanPromotionRepo } from '~/domains/primary/package-plan-promotion/package-plan-promotion.repo';

@Injectable()
export class PackagePlanService {
  constructor() {
  }

  @BindRepo(PackagePlanRepo)
  private packagePlanRepo: PackagePlanRepo;

  @BindRepo(PackagePlanPromotionRepo)
  private packagePlanPromotionRepo: PackagePlanPromotionRepo;

  async findAll() {
    const promotions = await this.packagePlanPromotionRepo.find({ where: { billingCycle: 1 } });
    const packages = await this.packagePlanRepo.find({ where: { status: NSPackage.EStatus.ACTIVE } });
    return packages.map((pkg) => {
      const promotion = promotions.find((p) => p.packagePlanId === pkg.id);
      return {
        ...pkg,
        promotion,
      };
    });
  }

  async findPagination(body: PackagePlanListDto) {
    const { name, status, packagePlanId, pageIndex, pageSize } = body;
    const where: any = { status: NSPackage.EStatus.ACTIVE }
    if (name) {
      where.name = name;
    }
    if (status) {
      where.status = status;
    }
    if (packagePlanId) {
      where.packagePlanId = packagePlanId;
    }
    return await this.packagePlanRepo.findPagination({ where }, { pageIndex, pageSize });
  }
}
