
import {
  Body,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { PackagePlanService } from './package-plan.service';
import { PackagePlanListDto } from './dto/package.dto';
import { DefController, DefGet, DefPost } from '~/@core/decorator';

@ApiTags('Package-plans')
@DefController('package-plans')
export class PackagePlanController {
  constructor(private readonly packagePlanService: PackagePlanService) { }

  @DefGet("", {
    summary: 'L<PERSON>y danh sách gói dịch vụ',
  })
  findAll() {
    return this.packagePlanService.findAll();
  }

  @DefPost('pagination', {
    summary: 'L<PERSON>y danh sách gói dịch vụ của member',
  })
  findPagination(@Body() body: PackagePlanListDto) {
    return this.packagePlanService.findPagination(body);
  }
}
