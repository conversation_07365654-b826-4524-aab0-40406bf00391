import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPositive,
  IsString,
  IsInt,
  Min,
  IsBoolean,
  IsUUID,
} from 'class-validator';
import { PageRequest } from '~/@systems/utils/page.utils';
import { NSPackage } from '~/common/enums/package.enum';

export class CreatePackagePlanDto {
  @ApiProperty({ description: 'Tên gói dịch vụ' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ description: 'Giá gốc' })
  @IsNumber()
  @Min(0)
  originalPrice: number;

  @ApiProperty({ description: '<PERSON>i<PERSON> bán theo tháng' })
  @IsNumber()
  @Min(0)
  sellPriceMonthly: number;

  @ApiProperty({ description: 'Gi<PERSON> bán theo năm' })
  @IsNumber()
  @Min(0)
  sellPriceYearly: number;

  @ApiProperty({ description: '<PERSON><PERSON> lượng giao dịch cho phép' })
  @IsInt()
  @Min(0)
  transactionLimit: number;

  @ApiProperty({ description: 'Số lượng cấu hình cho phép' })
  @IsInt()
  @Min(0)
  configLimit: number;

  @ApiPropertyOptional({ description: 'Mô tả gói dịch vụ' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Ghi chú' })
  @IsOptional()
  @IsString()
  note?: string;

  @ApiPropertyOptional({ description: 'Là phổ biến nhất' })
  @IsOptional()
  @IsBoolean()
  isMostPopular?: boolean;
}

export class UpdatePackagePlanDto extends CreatePackagePlanDto {
  @ApiProperty({ description: 'ID của gói dịch vụ' })
  @IsNotEmpty()
  @IsString()
  id: string;
}

export class PackagePlanListDto extends PageRequest {
  //code
  @ApiProperty({ description: 'Mã gói dịch vụ' })
  @IsOptional()
  code?: string;

  @ApiProperty({ description: 'Tên gói dịch vụ' })
  @IsOptional()
  name?: string;

  @ApiProperty({ description: 'Giá gốc' })
  @IsOptional()
  originalPrice?: number;

  @ApiProperty({ description: 'Giá bán theo tháng' })
  @IsOptional()
  sellPriceMonthly?: number;

  @ApiProperty({ description: 'Giá bán theo năm' })
  @IsOptional()
  sellPriceYearly?: number;

  @ApiProperty({ description: 'Số lượng giao dịch cho phép' })
  @IsOptional()
  transactionLimit?: number;

  @ApiPropertyOptional({ description: 'Mô tả gói dịch vụ' })
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({ description: 'Trạng thái' })
  @IsOptional()
  status?: NSPackage.EStatus;

  @ApiPropertyOptional({ description: 'Ngày tạo' })
  @IsOptional()
  createdDateFrom?: Date;

  @ApiPropertyOptional({ description: 'Ngày tạo' })
  @IsOptional()
  createdDateTo?: Date;

  @ApiPropertyOptional({ description: 'ID của gói dịch vụ' })
  @IsOptional()
  packagePlanId?: string;
}

export class PackagePlanDto {
  @ApiProperty({ description: 'ID của gói dịch vụ' })
  @IsNotEmpty()
  @IsUUID()
  id: string;

  @ApiProperty({ description: 'Tên gói dịch vụ' })
  @IsNotEmpty()
  @IsString()
  name: string;

  //status
  @ApiProperty({ description: 'Trạng thái' })
  @IsNotEmpty()
  status: string;

  @ApiProperty({ description: 'Giá bán theo tháng' })
  @IsNotEmpty()
  sellPriceMonthly: number;

  @ApiProperty({ description: 'Giá bán theo năm' })
  @IsNotEmpty()
  sellPriceYearly: number;

  @ApiProperty({ description: 'Số lượng giao dịch cho phép' })
  @IsNotEmpty()
  transactionLimit: number;

  @ApiProperty({ description: 'Số lượng cấu hình cho phép' })
  @IsNotEmpty()
  configLimit: number;

  @ApiProperty({ description: 'Số lượng giao dịch cho phép (năm)' })
  @IsNotEmpty()
  transactionLimitYearly: number;

  @ApiProperty({ description: 'Số lượng cấu hình cho phép (năm)' })
  @IsNotEmpty()
  configLimitYearly: number;
}
