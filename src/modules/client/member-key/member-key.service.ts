import { Injectable } from '@nestjs/common';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { ConfigApiRepo, MemberKeyRepo } from '~/domains/primary';
import { MemberRepo } from '~/domains/primary';
import { NSMember, NSMemberKey } from '~/common/enums';
import { ListProjectDto, MemberKeyDto, UpdateActiveDto } from './dto';
import { memberSessionContext } from '../member-session.context';
import { ConfigApiService } from '../config-api/config-api.service';
import { MemberApiRepo } from '~/domains/primary/member-api/member-api.repo';
import { MemberPackageRepo } from '~/domains/primary/member-package/member-package.repo';
import { Between, ILike } from 'typeorm';
import { BusinessException } from '~/@systems/exceptions';

@Injectable()
export class MemberKeyService {
    constructor(private configApiService: ConfigApiService) {}

    @BindRepo(MemberKeyRepo)
    private memberKeyRepo: MemberKeyRepo;

    @BindRepo(MemberRepo)
    private memberRepo: MemberRepo;

    @BindRepo(MemberApiRepo)
    private memberApiRepo: MemberApiRepo;

    @BindRepo(ConfigApiRepo)
    private configApiRepo: ConfigApiRepo;

    @BindRepo(MemberPackageRepo)
    private memberPackageRepo: MemberPackageRepo;

    @DefTransaction()
    async listProject(body: ListProjectDto) {
        const { memberId } = memberSessionContext;
        const { projectName, projectCode, status, createdDateFrom, createdDateTo, ...pageRequest } =
            body;
        const where: any = { memberId };
        if (projectName) {
            where.projectName = ILike(`%${projectName}%`);
        }
        if (projectCode) {
            where.projectCode = ILike(`%${projectCode}%`);
        }
        if (status) {
            where.status = status;
        }
        if (createdDateFrom && createdDateTo) {
            where.createdDate = Between(createdDateFrom, createdDateTo);
        }
        const { data, total } = await this.memberKeyRepo.findPagination(
            { where, order: { createdDate: 'DESC' } },
            pageRequest,
        );

        // Mapping data lấy ra số lượng cấu hình
        const mapping = data.map(async item => {
            const countConfig = await this.configApiRepo.count({ where: { memberKeyId: item.id } });
            return {
                ...item,
                countConfig,
            };
        });

        return {
            data: await Promise.all(mapping),
            total,
        };
    }

    // Khai báo key và cập nhật trạng thái member
    @DefTransaction()
    async generateKey(body: MemberKeyDto) {
        const { memberId } = memberSessionContext;
        const member = await this.memberRepo.findOne({ where: { id: memberId } });
        if (!member) throw new BusinessException('member_key.member_not_found');

        // Create member-key
        await this.memberKeyRepo.save({
            ...body,
            memberId,
        });

        // Update status member
        await this.memberRepo.update(
            { id: memberId },
            { statusValidate: NSMember.EStatus.VALIDATED },
        );

        // Update member-package limitProject
        const currentMP = await this.memberPackageRepo.findOne({ where: { memberId } });
        if (!currentMP) throw new BusinessException('member_key.member_package_not_found');
        if (currentMP.projectLimit < currentMP.currentProject + 1) {
            throw new BusinessException('member_key.limit_project_reached');
        }
        await this.memberPackageRepo.update(
            { memberId },
            { currentProject: currentMP.currentProject + 1 },
        );

        return { message: 'success' };
    }

    // Update
    @DefTransaction()
    async updateKey(body: MemberKeyDto) {
        const { memberId } = memberSessionContext;
        const { memberKeyId } = body;
        const member = await this.memberRepo.findOne({ where: { id: memberId } });
        if (!member) throw new BusinessException('member_key.member_not_found');

        // Nếu apiKey khong giong nhau thi thì mới gen lại
        const memberKey = await this.memberKeyRepo.findOne({ where: { id: memberKeyId } });
        if (memberKey.apiKey !== body.apiKey) {
            // Lấy ra toàn bộ member-api để gen lại
            const memberApis = await this.memberApiRepo.find({ where: { memberId } });
            for (const api of memberApis) {
                await this.configApiService.generateMemberAPIConfig(api.configApiId, api.memberKeyId);
            }
        }
        delete body.memberKeyId;
        await this.memberKeyRepo.update({ id: memberKeyId, memberId }, body);
        return { message: 'success' };
    }

    @DefTransaction()
    async updateActive(body: UpdateActiveDto) {
        const { id } = body;
        await this.memberKeyRepo.update({ id }, { status: NSMemberKey.EStatus.ACTIVE });
        return { message: 'success' };
    }

    @DefTransaction()
    async updateInActive(body: UpdateActiveDto) {
        const { id } = body;
        await this.memberKeyRepo.update({ id }, { status: NSMemberKey.EStatus.INACTIVE });
        return { message: 'success' };
    }
}
