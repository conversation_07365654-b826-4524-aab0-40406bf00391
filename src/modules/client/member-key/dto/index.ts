import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional } from "class-validator";
import { PageRequest } from "~/@systems/utils";

export class UpdateActiveDto {
    @ApiProperty({ description: 'ID của dự án' })
    id: string;
}

export class MemberKeyDto {
    @ApiProperty({ description: 'ID của dự án' })
    @IsOptional()
    memberKeyId?: string;

    @ApiProperty({ description: 'Public key' })
    publicKey: string;

    @ApiProperty({ description: 'API key' })
    apiKey: string;

    @ApiProperty({ description: 'Tên dự án' })
    @IsNotEmpty()
    projectName: string;

    @ApiPropertyOptional({ description: 'Mô tả dự án' })
    @IsOptional()
    projectDescription?: string;

    @ApiPropertyOptional({ description: 'Mã dự án' })
    @IsOptional()
    projectCode?: string;
}

export class ListProjectDto extends PageRequest {
    @ApiPropertyOptional({ description: 'Tên dự án' })
    @IsOptional()
    projectName: string;

    @ApiPropertyOptional({ description: 'Mã dự án' })
    @IsOptional()
    projectCode: string;

    @ApiPropertyOptional({ description: 'Trạng thái' })
    @IsOptional()
    status: string;

    @ApiPropertyOptional({ description: 'Ngày tạo từ' })
    @IsOptional()
    createdDateFrom: Date;

    @ApiPropertyOptional({ description: 'Ngày tạo đến' })
    @IsOptional()
    createdDateTo: Date;
}
