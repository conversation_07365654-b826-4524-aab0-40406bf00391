import { DefController, DefPost } from "~/@core/decorator";
import { MemberKeyService } from "./member-key.service";
import { Body } from "@nestjs/common";
import { ListProjectDto, MemberKeyDto, UpdateActiveDto } from "./dto";

@DefController('member-key')
export class Member<PERSON>eyController {
    constructor(private readonly service: MemberKeyService) { }

    @DefPost('create-project', {
        summary: 'Khởi tạo dự án',
    })
    generateKey(@Body() body: MemberKeyDto) {
        return this.service.generateKey(body);
    }

    // list-project
    @DefPost('list-project', {
        summary: '<PERSON><PERSON><PERSON> danh sách dự án',
    })
    listProject(@Body() body: ListProjectDto) {
        return this.service.listProject(body);
    }

    @DefPost('update', {
        summary: 'Cập nhật project',
    })
    updateKey(@Body() body: MemberKeyDto) {
        return this.service.updateKey(body);
    }

    // Update active, inactive
    @DefPost('active', {
        summary: 'Cập nhật trạng thái project',
    })
    updateActive(@Body() body: UpdateActiveDto) {
        return this.service.updateActive(body);
    }

    @DefPost('inactive', {
        summary: 'Cập nhật project',
    })
    updateInActive(@Body() body: UpdateActiveDto) {
        return this.service.updateInActive(body);
    }

}