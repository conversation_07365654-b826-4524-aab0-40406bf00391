import { Param, Query } from "@nestjs/common";
import { DefController, DefGet } from "~/@core/decorator";
import { ApiDocContentService } from "~/modules/admin/api-doc-content/api-doc-content.service";
import { ApiDocContentListDto } from "~/modules/admin/api-doc-content/dto/api-doc-content.dto";

@DefController('api-doc-content')
export class ApiDocContentController {
    constructor(private readonly service: ApiDocContentService) { }

    @DefGet('list', {
        summary: 'Lấy danh sách API Doc Content',
    })
    findAll(@Query() query: ApiDocContentListDto) {
        return this.service.findAll(query);
    }

    @DefGet(':id', {
        summary: 'Lấy thông tin chi tiết API Doc Content',
    })
    findOne(@Param('id') id: string) {
        return this.service.findOne(id);
    }
}
