import { Injectable } from '@nestjs/common';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import {
    MemberPackageRepo,
    OrderRepo,
    MemberApiLogRepo,
    PackagePlanRepo,
    PaymentTransactionRepo,
    ConfigApiRepo,
    MemberRepo,
} from '~/domains/primary';
import { CostStatsDto, MemberPackagePTDto } from './dto/member-package.dto';
import { memberSessionContext } from '../member-session.context';
import { NSConfig, NSMember, NSOrder, NSPackage, NSPayment } from '~/common/enums';
import Stripe from 'stripe';
import { configEnv } from '~/@config/env';
import { Between, LessThan, MoreThan } from 'typeorm';
import * as dayjs from 'dayjs';
import { BusinessException } from '~/@systems/exceptions';
import { PackagePlanPromotionRepo } from '~/domains/primary/package-plan-promotion/package-plan-promotion.repo';

@Injectable()
export class MemberPackageService {
    private stripe: Stripe;
    constructor() {
        this.stripe = new Stripe(configEnv().STRIPE_SECRET_KEY, {});
    }

    @BindRepo(MemberPackageRepo)
    private memberPackageRepo: MemberPackageRepo;

    @BindRepo(OrderRepo)
    private orderRepo: OrderRepo;

    @BindRepo(PaymentTransactionRepo)
    private paymentTransactionRepo: PaymentTransactionRepo;

    @BindRepo(MemberApiLogRepo)
    private memberApiLogRepo: MemberApiLogRepo;

    @BindRepo(PackagePlanRepo)
    private packagePlanRepo: PackagePlanRepo;

    @BindRepo(ConfigApiRepo)
    private configApiRepo: ConfigApiRepo;

    @BindRepo(MemberRepo)
    private memberRepo: MemberRepo;

    @BindRepo(PackagePlanPromotionRepo)
    private packagePlanPromotionRepo: PackagePlanPromotionRepo;

    async findOne() {
        const { memberId } = memberSessionContext;
        const packageMember = await this.memberPackageRepo.findOne({ where: { memberId } });
        if (!packageMember) return {};

        const plan = await this.packagePlanRepo.findOne({
            where: { id: packageMember.packagePlanId },
        });
        if (!plan) throw new BusinessException('member_package.plan_not_found');

        const order = await this.orderRepo.find({ where: { memberId: memberId } });
        if (!order) throw new BusinessException('member_package.order_not_found');

        let subscriptions = null;
        let invoicesSubscription = null;
        if (packageMember.subscriptionId) {
            subscriptions = await this.stripe.subscriptions.retrieve(packageMember.subscriptionId);
            invoicesSubscription = await this.stripe.invoices.list({
                subscription: packageMember.subscriptionId,
                status: 'paid',
            });
        }

        // Invoice buy, lấy từ đơn hàng mua gói
        let invoiceBuy = null;
        const orderBuyPlan = await this.orderRepo.findOne({
            where: { memberId: memberId , orderType: NSOrder.EOrderType.PACKAGE },
        });
        if (orderBuyPlan?.invoiceProvider === NSPayment.EPaymentProvider.STRIPE) {
            invoiceBuy = await this.stripe.invoices.retrieve(orderBuyPlan.invoiceId);
        }

        const apiLogs = await this.memberApiLogRepo.find({
            where: { memberPackageId: packageMember.id },
            order: { createdDate: 'DESC' },
            take: 5,
        });

        return {
            package: packageMember,
            plan,
            order,
            subscriptions,
            invoicesSubscription,
            invoiceBuy,
            log: apiLogs,
        };
    }

    async suggestUpgrade() {
        const { memberId } = memberSessionContext;
        const packageMember = await this.memberPackageRepo.findOne({
            where: { memberId, status: NSMember.EMemberPackageStatus.ACTIVE },
        });
        if (!packageMember) {
            return [];
        }

        const plan = await this.packagePlanRepo.findOne({
            where: { id: packageMember.packagePlanId },
        });
        if (!plan) throw new BusinessException('payment.plan_not_found');

        const plans = await this.packagePlanRepo.find({
            where: {
                status: NSPackage.EStatus.ACTIVE,
                originalPrice: MoreThan(plan.originalPrice),
            },
            order: { originalPrice: 'ASC' },
        });

        return plans;
    }

    async suggestDowngrade() {
        const { memberId } = memberSessionContext;
        const packageMember = await this.memberPackageRepo.findOne({
            where: { memberId, status: NSMember.EMemberPackageStatus.ACTIVE },
        });
        if (!packageMember) {
            return [];
        }

        const plan = await this.packagePlanRepo.findOne({
            where: { id: packageMember.packagePlanId },
        });
        if (!plan) throw new BusinessException('member_package.plan_not_found');

        const plans = await this.packagePlanRepo.find({
            where: {
                status: NSPackage.EStatus.ACTIVE,
                originalPrice: LessThan(plan.originalPrice)
            },
        });

        return plans;
    }

    // Danh sách giao dịch của một member-package
    async findTransaction(body: MemberPackagePTDto) {
        const { memberPackageId, ...pageRequest } = body;
        const packageMember = await this.memberPackageRepo.findOne({
            where: { id: memberPackageId },
        });
        if (!packageMember) throw new BusinessException('member_package.not_found');
        return this.paymentTransactionRepo.findPagination(
            { where: { memberPackageId: packageMember.id } },
            pageRequest,
        );
    }

    // Hủy sử dụng gói
    @DefTransaction()
    async cancelMemberPackage(id: string) {
        const memberPackage = await this.memberPackageRepo.findOne({ where: { id } });
        if (!memberPackage) throw new BusinessException('member_package.not_found');

        // Inactive toàn bộ cấu hình
        await this.configApiRepo.update(
            { memberId: memberPackage.memberId },
            { status: NSConfig.EStatus.INACTIVE },
        );

        // Hủy tự động gia hạn
        await this.cancelAutoRenewal(id);

        return await this.memberPackageRepo.update(
            { id },
            { status: NSMember.EMemberPackageStatus.CANCELED },
        );
    }

    // Hủy tự động gia hạn gói bằng subscriptionId trên Stripe
    @DefTransaction()
    async cancelAutoRenewal(id: string) {
        const memberPackage = await this.memberPackageRepo.findOne({ where: { id } });

        // Kiểm tra member package có tồn tại không
        if (!memberPackage) throw new BusinessException('member_package.not_found');

        // Kiểm tra trạng thái hiện tại
        if (memberPackage.status === NSMember.EMemberPackageStatus.CANCELED) {
            return { message: 'Member package already canceled' };
        }

        // Kiểm tra trạng thái đã hết hạn
        if (memberPackage.status === NSMember.EMemberPackageStatus.EXPIRED) {
            return { message: 'Member package already expired' };
        }

        // Lấy subscriptionId trên Stripe
        const { subscriptionId } = memberPackage;

        // Hủy subscription trên Stripe nếu có
        if (subscriptionId) {
            await this.stripe.subscriptions.cancel(subscriptionId);
        }

        // Cập nhật database một lần duy nhất
        await this.memberPackageRepo.update(
            { id },
            {
                subscriptionProvider: null,
                subscriptionId: null,
                isAutoRenewal: false,
            },
        );

        return { message: 'Cancel auto renewal successfully' };
    }

    // Bật tự động gia hạn gói bằng subscriptionId trên Stripe
    @DefTransaction()
    async enableAutoRenewal(id: string) {
        const memberPackage = await this.memberPackageRepo.findOne({ where: { id } });

        // Kiểm tra member package có tồn tại không
        if (!memberPackage) throw new BusinessException('member_package.not_found'); 

        // Kiểm tra trạng thái hiện tại
        if (memberPackage.status === NSMember.EMemberPackageStatus.CANCELED) {
            return { message: 'Member package already canceled' };
        }

        // Kiểm tra trạng thái đã hết hạn
        if (memberPackage.status === NSMember.EMemberPackageStatus.EXPIRED) {
            return { message: 'Member package already expired' };
        }

        // Lấy thông tin plan
        const plan = await this.packagePlanRepo.findOne({
            where: { id: memberPackage.packagePlanId },
        });
        if (!plan) throw new BusinessException('member_package.plan_not_found');
        const promotion = await this.packagePlanPromotionRepo.findOne({
            where: { packagePlanId: plan.id, billingCycle: 1 },
        });

        const priceId = promotion.stripePriceId
                
        //tìm member theo memberId
        const member = await this.memberRepo.findOne({ where: { id: memberPackage.memberId } });
        if (!member) throw new BusinessException('member_package.member_not_found');

        // Kiểm tra hoặc tạo Stripe customer
        let customer: Stripe.Customer;
        const customers = await this.stripe.customers.list({ email: member.email, limit: 1 });
        customer =
            customers.data[0] ||
            (await this.stripe.customers.create({
                email: member.email,
            }));
        const periodDay = 30;
        //lấy khoảng cách từ ngày hiện tại so với ngày kích hoạt
        const diff = dayjs()
            .subtract(memberPackage.activatedDate.getDate() + 1, 'day')
            .startOf('day')
            .unix();

        // Hủy subscription trên Stripe nếu có
        const newSubscription = await this.stripe.subscriptions.create({
            customer: customers.data[0].id,
            items: [{ price: priceId }],
            trial_period_days: periodDay,
            backdate_start_date: diff,
            proration_behavior: 'none',
        });

        // Cập nhật database một lần duy nhất
        await this.memberPackageRepo.update(
            { id },
            {
                subscriptionProvider: NSPayment.EPaymentProvider.STRIPE,
                subscriptionId: newSubscription.id,
                nextPaymentDate: memberPackage.expiredDate,
                isAutoRenewal: true,
            },
        );

        return { message: 'Create auto renewal successfully' };
    }

    async getCostStats(body: CostStatsDto) {
        const { memberId } = memberSessionContext;
        const { startDate, endDate } = body;

        const wheres: any = {};
        if (startDate && endDate) {
            wheres.transactionDate = Between(startDate, endDate);
        }

        const { data, total } = await this.paymentTransactionRepo.findPagination(
            {
                where: { memberId, status: NSPayment.ETransactionStatus.COMPLETED, ...wheres },
                order: { transactionDate: 'DESC' },
            },
            { pageIndex: 1, pageSize: 1000 },
        );

        const totalCost = await this.paymentTransactionRepo.sum({
            where: { memberId, status: NSPayment.ETransactionStatus.COMPLETED },
            sumSelect: ['amount'],
        });
        // Lấy tổng số tiền
        return {
            ...totalCost,
            total: total,
            data: data,
        };
    }
}
