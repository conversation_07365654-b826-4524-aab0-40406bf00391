import { DefController, DefPost } from '~/@core/decorator';
import { MemberPackageService } from './member-package.service';
import { MemberPackagePTDto } from './dto/member-package.dto';
import { Body } from '@nestjs/common';

@DefController('member-package')
export class MemberPackageController {
  constructor(private readonly service: MemberPackageService) {}

  @DefPost('detail', {
    summary: 'Lấy thông tin chi tiết gói dịch vụ của member',
  })
  findOne() {
    return this.service.findOne();
  }

  @DefPost('suggest-upgrade', {
    summary: '<PERSON><PERSON>y danh sách gợi ý gói dịch vụ cao hơn',
  })
  suggest() {
    return this.service.suggestUpgrade();
  }

  @DefPost('suggest-downgrade', {
    summary: 'Lấy danh sách gợi ý gói dịch vụ thấp hơn',
  })
  suggestDowngrade() {
    return this.service.suggestDowngrade();
  }

  @DefPost('transaction', {
    summary: '<PERSON><PERSON><PERSON> danh sách giao dịch của gói dịch vụ của member',
  })
  findTransaction(@Body() body: MemberPackagePTDto) {
    return this.service.findTransaction(body);
  }

  @DefPost('cancel-auto-renewal', {
    summary: 'Hủy tự động gia hạn gói dịch vụ',
  })
  cancelAutoRenewal(@Body('id') body: string) {
    return this.service.cancelAutoRenewal(body);
  }

  @DefPost('enable-auto-renewal', {
    summary: 'Bật tự động gia hạn gói dịch vụ',
  })
  enableAutoRenewal(@Body('id') body: string) {
    return this.service.enableAutoRenewal(body);
  }

  // Thống kê chi phí mua, gia hạn member-package
  @DefPost('cost-stats', {
    summary: 'Thống kê chi phí mua, gia hạn gói dịch vụ',
  })
  getCostStats(@Body() body: any) {
    return this.service.getCostStats(body);
  }
}
