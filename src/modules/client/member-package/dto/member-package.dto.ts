import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsDate, IsOptional, IsString, IsUUID } from 'class-validator';
import { PageRequest } from '~/@systems/utils';

export class MemberPackageListDto extends PageRequest {
  @ApiProperty({ description: 'ID của đơn hàng' })
  @IsOptional()
  orderId?: string;

  @ApiProperty({ description: 'ID của gói dịch vụ' })
  @IsOptional()
  packagePlanId?: string;

  @ApiProperty({ description: 'Trạng thái' })
  @IsOptional()
  status?: string;

  @ApiProperty({ description: '<PERSON><PERSON>y hết hạn từ' })
  @IsOptional()
  expiredDateFrom?: Date;

  @ApiProperty({ description: 'Ngày hết hạn đến' })
  @IsOptional()
  expiredDateTo?: Date;

  @ApiProperty({ description: 'Tên gói dịch vụ' })
  @IsOptional()
  name?: string;
}

export class MemberPackagePTDto extends PageRequest {
  @ApiProperty({ description: 'ID của gói dịch vụ member đã mua' })
  @IsOptional()
  memberPackageId?: string;
}

export class MemberPackageDto {
  //id
  @ApiProperty({ description: 'ID của gói dịch vụ member đã mua' })
  @IsUUID()
  id: string;

  @ApiProperty({ description: 'ID của thành viên' })
  @IsUUID()
  memberId: string;

  @ApiProperty({ description: 'ID của gói dịch vụ member đã mua' })
  @IsUUID()
  memberPackageId: string;

  @ApiProperty({ description: 'ID của gói dịch vụ' })
  @IsUUID()
  packagePlanId: string;

  @ApiPropertyOptional({ description: 'Ngày hết hạn' })
  @IsDate()
  expiredDate: Date;

  @ApiPropertyOptional({ description: 'Ngày kích hoạt' })
  @IsDate()
  activatedDate: Date;

  @ApiPropertyOptional({ description: 'Ngày thanh toán tiếp theo' })
  @IsDate()
  nextPaymentDate: Date;

  @ApiProperty({ description: 'Số lượng giao dịch được phép' })
  initialTransactionLimit: number;

  @ApiProperty({ description: 'Số lượng giao dịch hiện tại' })
  currentTransaction: number;

  @ApiProperty({ description: 'Số lượng cấu hình được phép' })
  initialConfigLimit: number;

  @ApiProperty({ description: 'Số lượng cấu hình hiện tại' })
  currentConfig: number;

  @ApiProperty({ description: 'Trạng thái' })
  @IsString()
  status: string;

  @ApiProperty({ description: 'Là gói dịch vụ tự động gia hạn' })
  @IsBoolean()
  isAutoRenewal: boolean;

  @ApiProperty({ description: 'ID của subscription' })
  @IsString()
  subscriptionId: string;

  @ApiProperty({ description: 'Đối tác cung cấp subscription' })
  @IsString()
  subscriptionProvider: string;
}

export class CostStatsDto extends PageRequest {
    @ApiProperty({ description: 'Ngày bắt đầu' })
    @IsDate()
    startDate: Date;
  
    @ApiProperty({ description: 'Ngày kết thúc' })
    @IsDate()
    endDate: Date;
}
