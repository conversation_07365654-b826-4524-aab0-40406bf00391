import { Def<PERSON><PERSON><PERSON>er, DefPost, DefGet } from '~/@core/decorator';
import { TwoFactorService } from './two-factor.service';
import { Body, Req } from '@nestjs/common';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../@providers/jwt-auth.guard';
import * as express from 'express';
import {
    Enable2FAReq,
    Disable2FAReq,
    Verify2FAReq,
    GenerateQRCodeRes,
    TwoFactorStatusRes,
} from './dto/two-factor.dto';

@DefController('2fa')
@UseGuards(JwtAuthGuard)
export class TwoFactorController {
    constructor(private readonly twoFactorService: TwoFactorService) {}

    @DefGet('generate-qr', {
        summary: 'Generate QR code for 2FA setup',
    })
    async generateQRCode(
        @Req() req: express.Request & { user: { id: string } },
    ): Promise<GenerateQRCodeRes> {
        return this.twoFactorService.generateQRCode(req.user.id);
    }

    @DefPost('enable', {
        summary: 'Enable 2FA',
    })
    async enable2FA(
        @Body() body: Enable2FAReq,
        @Req() req: express.Request & { user: { id: string } },
    ): Promise<{ message: string }> {
        await this.twoFactorService.enable2FA(req.user.id, body.code, body.secret);
        return { message: '2FA enabled successfully' };
    }

    @DefPost('disable', {
        summary: 'Disable 2FA',
    })
    async disable2FA(
        @Body() body: Disable2FAReq,
        @Req() req: express.Request & { user: { id: string } },
    ): Promise<{ message: string }> {
        await this.twoFactorService.disable2FA(req.user.id, body.code);
        return { message: '2FA disabled successfully' };
    }

    @DefPost('verify', {
        summary: 'Verify 2FA code',
    })
    async verify2FA(
        @Body() body: Verify2FAReq,
        @Req() req: express.Request & { user: { id: string } },
    ): Promise<{ valid: boolean }> {
        const isValid = await this.twoFactorService.verify2FA(req.user.id, body.code);
        return { valid: isValid };
    }

    @DefGet('status', {
        summary: 'Get 2FA status',
    })
    async get2FAStatus(
        @Req() req: express.Request & { user: { id: string } },
    ): Promise<TwoFactorStatusRes> {
        return this.twoFactorService.get2FAStatus(req.user.id);
    }
}
