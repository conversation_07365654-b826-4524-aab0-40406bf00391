import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, Length } from 'class-validator';

export class Enable2FAReq {
    @ApiProperty({ example: '123456' })
    @IsNotEmpty()
    @IsString()
    @Length(6, 6)
    code: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    secret: string;
}

export class Disable2FAReq {
    @ApiProperty({ example: '123456' })
    @IsNotEmpty()
    @IsString()
    @Length(6, 6)
    code: string;
}

export class Verify2FAReq {
    @ApiProperty({ example: '123456' })
    @IsNotEmpty()
    @IsString()
    @Length(6, 6)
    code: string;
}

export class GenerateQRCodeRes {
    @ApiProperty()
    qrCode: string;

    @ApiProperty()
    secret: string;

    @ApiProperty({ type: [String] })
    backupCodes: string[];
}

export class TwoFactorStatusRes {
    @ApiProperty()
    enabled: boolean;

    @ApiProperty({ required: false })
    enabledAt?: Date;
}
