import { Injectable } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import { MemberRepo } from '~/domains/primary';
import { BusinessException } from '~/@systems/exceptions';
import { authenticator } from 'otplib';

@Injectable()
export class TwoFactorService {
    @BindRepo(MemberRepo)
    private memberRepo: MemberRepo;

    /**
     * Xác thực TOTP code
     */
    private verifyTOTP(secret: string, code: string): boolean {
        return authenticator.verify({
            secret,
            token: code,
        });
    }

    /**
     * Tạo QR code với format đầy đủ cho Google Authenticator
     */
    async generateQRCode(
        memberId: string,
    ): Promise<{ qrCode: string; secret: string; backupCodes: string[] }> {
        const member = await this.memberRepo.findOne({ where: { id: memberId } });
        if (!member) {
            throw new BusinessException('member_not_existed');
        }

        if (member.twoFactorEnabled) {
            throw new BusinessException('2fa.already_enabled');
        }

        const secret = authenticator.generateSecret();

        const otpauth = authenticator.keyuri(member.email, 'APEX-LINK', secret);
        // const backupCodes = this.generateBackupCodes(); // Không truyền otpauth

        const qrCode = otpauth;

        return {
            qrCode,
            secret,
            backupCodes: [],
        };
    }

    // /**
    //  * Tạo backup codes
    //  */
    // private generateBackupCodes(): string[] {
    //     const codesBackup = authenticator.generate()

    //     return codesBackup;
    // }

    /**
     * Kích hoạt 2FA
     */
    async enable2FA(memberId: string, code: string, secret: string): Promise<void> {
        const member = await this.memberRepo.findOne({ where: { id: memberId } });
        if (!member) {
            throw new BusinessException('member_not_existed');
        }

        if (member.twoFactorEnabled) {
            throw new BusinessException('2fa.already_enabled');
        }

        // Xác thực code
        if (!this.verifyTOTP(secret, code)) {
            throw new BusinessException('2fa.invalid_code');
        }

        // Lưu secret và kích hoạt 2FA
        await this.memberRepo.update(memberId, {
            twoFactorSecret: secret,
            twoFactorEnabled: true,
            twoFactorEnabledAt: new Date().toISOString(),
        });
    }

    /**
     * Vô hiệu hóa 2FA
     */
    async disable2FA(memberId: string, code: string): Promise<void> {
        const member = await this.memberRepo.findOne({ where: { id: memberId } });
        if (!member) {
            throw new BusinessException('member_not_existed');
        }

        if (!member.twoFactorEnabled || !member.twoFactorSecret) {
            throw new BusinessException('2fa.not_enabled');
        }

        // Xác thực code
        if (!this.verifyTOTP(member.twoFactorSecret, code)) {
            throw new BusinessException('2fa.invalid_code');
        }

        // Vô hiệu hóa 2FA
        await this.memberRepo.update(memberId, {
            twoFactorSecret: null,
            twoFactorEnabled: false,
            twoFactorEnabledAt: null,
        });
    }

    /**
     * Xác thực 2FA code
     */
    async verify2FA(memberId: string, code: string): Promise<boolean> {
        const member = await this.memberRepo.findOne({ where: { id: memberId } });
        if (!member) {
            throw new BusinessException('member_not_existed');
        }

        if (!member.twoFactorEnabled || !member.twoFactorSecret) {
            throw new BusinessException('2fa.not_enabled');
        }

        return this.verifyTOTP(member.twoFactorSecret, code);
    }

    /**
     * Lấy trạng thái 2FA
     */
    async get2FAStatus(memberId: string): Promise<{ enabled: boolean; enabledAt?: Date }> {
        const member = await this.memberRepo.findOne({ where: { id: memberId } });
        if (!member) {
            throw new BusinessException('member_not_existed');
        }

        return {
            enabled: member.twoFactorEnabled,
            enabledAt: member.twoFactorEnabledAt ? new Date(member.twoFactorEnabledAt) : undefined,
        };
    }
}
