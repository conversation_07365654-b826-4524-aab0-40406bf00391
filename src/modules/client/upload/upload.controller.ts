import {
  Controller,
  UploadedFile,
  UploadedFiles,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor, } from '@nestjs/platform-express';
import { ApiTags } from '@nestjs/swagger';
import { AwsS3Service } from '~/@core/services/aws-s3.service';
import { nanoid } from 'nanoid';
import { DefController, DefPost } from '~/@core/decorator';

@ApiTags('Upload')
@DefController('upload')
export class UploadController {
  constructor(private readonly awsS3Service: AwsS3Service) { }

  // upload single file
  @DefPost('upload_single_s3', {
    summary: 'Hàm upload file lên S3',
  })
  @UseInterceptors(FileInterceptor('file'))
  uploadSingleS3(@UploadedFile() file: Express.Multer.File) {
    return this.awsS3Service.uploadFile(file.buffer, nanoid(10), file.mimetype);
  }

  // upload multiple files
  @DefPost('upload_multiple_s3', {
    summary: 'Hàm upload file lên S3',
  })
  @UseInterceptors(FileInterceptor('file'))
  uploadMultipleS3(@UploadedFiles() files: Express.Multer.File[]) {
    return this.awsS3Service.uploadMultipleFiles(
      files.map(file => ({
        file: file.buffer,
        key: nanoid(10),
        contentType: file.mimetype,
      })),
    );
  }
}
