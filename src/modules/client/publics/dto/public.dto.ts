import { ApiProperty } from '@nestjs/swagger';
import { NSOrder } from '~/common/enums/order.enum';
import { IsEnum, IsNotEmpty, IsNumber, IsOptional, IsUUID } from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSPayment } from '~/common/enums/payment.enum';
import { NSPackage } from '~/common/enums';

export class PublicReportDto {
  @ApiProperty()
  @IsNumber()
  totalMember: number;

  @ApiProperty()
  @IsNumber()
  totalTransaction: number;

  @ApiProperty()
  @IsNumber()
  totalConfigPackage: number;

  @ApiProperty()
  @IsNumber()
  totalMemberPackage: number;
}
