import { Injectable } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import { NSPayment } from '~/common/enums';
import { ConfigApiRepo, PaymentTransactionRepo } from '~/domains/primary';
import { MemberRepo } from '~/domains/primary';
import { MemberPackageRepo } from '~/domains/primary/member-package/member-package.repo';
import { configEnv } from '~/@config/env';
import { optionalApiConnector } from '~/connectors/api.connector';

@Injectable()
export class PublicService {
    constructor() {}

    @BindRepo(ConfigApiRepo)
    private configApiRepo: ConfigApiRepo;

    @BindRepo(MemberRepo)
    private memberRepo: MemberRepo;

    @BindRepo(MemberPackageRepo)
    private memberPackageRepo: MemberPackageRepo;

    @BindRepo(PaymentTransactionRepo)
    private paymentTransactionRepo: PaymentTransactionRepo;

    async totalReport() {
        const [totalMember, totalConfigPackage, totalTransaction, totalMemberPackage] =
            await Promise.all([
                this.memberRepo.count(), // Số lượng thành viên
                this.configApiRepo.count(), // Số lượng cấu hình member đã tạo
                this.paymentTransactionRepo.count({
                    where: { status: NSPayment.ETransactionStatus.COMPLETED },
                }), // Số lượng giao dịch thanh toán
                this.memberPackageRepo.count(), // Số lượng gói dịch vụ member đã mua
            ]);

        return {
            totalMember,
            totalTransaction,
            totalConfigPackage,
            totalMemberPackage,
        };
    }

    async getEthGasFromEtherscan() {
        const { ETHERSCAN_URL, ETHERSCAN_API_KEY } = configEnv();
        const url = `${ETHERSCAN_URL}?module=gastracker&action=gasoracle&apikey=${ETHERSCAN_API_KEY}`;
        const response = await optionalApiConnector.get(url);
        return response?.result;
    }

    async getBscGasFromRpc() {
        const { BSC_RPC_URL } = configEnv();

        const payload = {
            jsonrpc: '2.0',
            method: 'eth_gasPrice',
            params: [],
            id: 1,
        };

        const response = await optionalApiConnector.post(BSC_RPC_URL, payload);
        const hexWei = response?.result;

        if (!hexWei) return null;

        const gasWei = parseInt(hexWei, 16);
        const gasGwei = gasWei / 1e9;

        return {
            gasPriceWei: gasWei,
            gasPriceGwei: Math.round(gasGwei * 100) / 100, // làm tròn 2 chữ số
        };
    }

    async getDisplayGasPrices() {
        try {
            const [ethOracle, bnbGas] = await Promise.all([
                this.getEthGasFromEtherscan(), // Gwei string
                this.getBscGasFromRpc(), // { gasPriceWei, gasPriceGwei }
            ]);

            const gasLimit = 50000;

            // ETH
            const ethGwei = parseFloat(ethOracle?.ProposeGasPrice || '0');
            const ethWei = ethGwei * 1e9;
            const ethGasFee = (ethWei * gasLimit) / 1e18; // đơn vị ETH

            // BNB
            const bnbGwei = bnbGas?.gasPriceGwei || 0;
            const bnbWei = bnbGwei * 1e9;
            const bnbGasFee = (bnbWei * gasLimit) / 1e18; // đơn vị BNB

            return {
                eth: {
                    gwei: Math.round(ethGwei * 100) / 100,
                    wei: Math.round(ethWei),
                    estimatedGasFee: Math.round(ethGasFee * 1e8) / 1e8, // làm tròn 8 chữ số thập phân
                    unit: 'ETH',
                },
                bnb: {
                    gwei: Math.round(bnbGwei * 100) / 100,
                    wei: Math.round(bnbWei),
                    estimatedGasFee: Math.round(bnbGasFee * 1e8) / 1e8,
                    unit: 'BNB',
                },
            };
        } catch (error) {
            return {
                eth: {
                    gwei: 0,
                    wei: 0,
                    estimatedGasFee: 0,
                    unit: 'ETH',
                },
                bnb: {
                    gwei: 0,
                    wei: 0,
                    estimatedGasFee: 0,
                    unit: 'BNB',
                },
            };
        }
    }
}
