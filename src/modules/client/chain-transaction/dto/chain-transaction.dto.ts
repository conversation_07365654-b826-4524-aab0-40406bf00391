import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsUUID } from 'class-validator';
import { v4 } from 'uuid';
import { PageRequest } from '~/@systems/utils';
import { N<PERSON>hain } from '~/common/enums/chain.enum';

export class ChainTransactionListReq extends PageRequest {
    @ApiPropertyOptional({ description: 'Keyword filter by txHash', example: '0x00' })
    keyword?: string;

    @ApiPropertyOptional({
        description: 'Status filter',
        example: NSChain.ETransactionStatus.COMPLETED,
    })
    status?: NSChain.ETransactionStatus;

    @ApiPropertyOptional({ description: 'From date filter', example: '2023-01-01' })
    fromDate?: Date;

    @ApiPropertyOptional({ description: 'To date filter', example: '2023-01-01' })
    toDate?: Date;

    @ApiPropertyOptional({ description: 'Config API ID', example: v4() })
    configApiId?: string;
}

export enum SummaryByTimeMode {
    THIS_WEEK = 'this-week',
    THIS_MONTH = 'this-month',
    THIS_YEAR = 'this-year',
}

export class SummaryByTimeReq {
    @ApiProperty()
    @IsUUID('4')
    configApiId: string;

    @ApiProperty({ description: 'Time mode', example: SummaryByTimeMode.THIS_WEEK })
    timeMode: SummaryByTimeMode;
}

export class LogRequestReq extends PageRequest {}
