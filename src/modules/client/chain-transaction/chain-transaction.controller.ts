import { Def<PERSON><PERSON>roller, DefPost } from '~/@core/decorator';
import { ChainTransactionService } from './chain-transaction.service';
import { Body } from '@nestjs/common';
import {
    ChainTransactionListReq,
    SummaryByTimeReq,
    LogRequestReq,
} from './dto/chain-transaction.dto';

@DefController('chain-transaction')
export class ChainTransactionController {
    constructor(private readonly service: ChainTransactionService) {}

    @DefPost('list')
    async list(@Body() params: ChainTransactionListReq) {
        return this.service.list(params);
    }

    @DefPost('summary')
    async summary() {
        return this.service.summary();
    }

    @DefPost('summary-by-time')
    async summaryByTime(@Body() body: SummaryByTimeReq) {
        return this.service.summaryByTime(body.timeMode);
    }

    @DefPost('log-request')
    async logRequest(@Body() body: LogRequestReq) {
        return this.service.logRequest(body);
    }
}
