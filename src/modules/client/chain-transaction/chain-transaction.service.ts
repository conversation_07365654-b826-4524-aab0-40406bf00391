import { Injectable } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import { ChainTransactionRepo, LogRequestRepo } from '~/domains/primary';
import {
    ChainTransactionListReq,
    LogRequestReq,
    SummaryByTimeMode,
} from './dto/chain-transaction.dto';
import { memberSessionContext } from '../member-session.context';
import { NSChain } from '~/common/enums/chain.enum';
import * as dayjs from 'dayjs';

@Injectable()
export class ChainTransactionService {
    constructor() {}

    @BindRepo(ChainTransactionRepo)
    private chainTransactionRepo: ChainTransactionRepo;

    @BindRepo(LogRequestRepo)
    private logRequestRepo: LogRequestRepo;

    list(body: ChainTransactionListReq) {
        const { memberId } = memberSessionContext;

        const wheres = [`ct."memberId" = '${memberId}'`];
        if (body.keyword) {
            wheres.push(`ct."txHash" ILIKE '%${body.keyword}%'`);
        }
        if (body.status) {
            wheres.push(`ct."status" = '${body.status}'`);
        }
        if (body.fromDate) {
            wheres.push(`ct."createdDate" >= '${body.fromDate}'`);
        }
        if (body.toDate) {
            wheres.push(`ct."createdDate" <= '${body.toDate}'`);
        }
        if (body.configApiId) {
            wheres.push(`ct."configApiId" = '${body.configApiId}'`);
        }
        const sql = `
            SELECT ct.*,m.email as "email",ca.name as "configApiName",ca."code" as "configApiCode" FROM chain_transaction ct 
            LEFT JOIN member m ON ct."memberId" = m.id
            LEFT JOIN config_api ca ON ct."configApiId" = ca.id
            WHERE ${wheres.join(' AND ')}
            ORDER BY ct."createdDate" DESC
            `;
        return this.chainTransactionRepo.queryPagination(sql, body);
    }

    async summary() {
        const { memberId } = memberSessionContext;
        const wheres = [`ct."memberId" = '${memberId}'`];
        wheres.push(`ct."type" = '${NSChain.ETransactionType.MAINNET}'`);
        const sql = `
            SELECT 
                COUNT(*) as "total",
                SUM(CASE WHEN ct."status" = '${NSChain.ETransactionStatus.COMPLETED}' THEN 1 ELSE 0 END) as "totalCompleted",
                SUM(CASE WHEN ct."status" = '${NSChain.ETransactionStatus.PENDING}' THEN 1 ELSE 0 END) as "totalPending",
                SUM(CASE WHEN ct."status" = '${NSChain.ETransactionStatus.FAILED}' THEN 1 ELSE 0 END) as "totalFailed",
                SUM(ct."fee") as "totalFee"
            FROM chain_transaction ct
            WHERE ${wheres.join(' AND ')}   
           `;
        const summary = await this.chainTransactionRepo.queryOne(sql).catch(() => ({
            total: 0,
            totalCompleted: 0,
            totalPending: 0,
            totalFailed: 0,
            totalFee: 0,
        }));
        return {
            total: Number(summary.total),
            totalCompleted: Number(summary.totalCompleted),
            totalPending: Number(summary.totalPending),
            totalFailed: Number(summary.totalFailed),
            totalFee: Number(summary.totalFee),
        };
    }

    async summaryByTime(timeMode: SummaryByTimeMode) {
        const { memberId } = memberSessionContext;
        const wheres = [
            `ct."memberId" = '${memberId}'`,
            `ct."type" = '${NSChain.ETransactionType.MAINNET}'`,
        ];

        let timeFormat = '';
        let labels: string[] = [];
        let startDate: string;

        const now = dayjs();

        if (timeMode === SummaryByTimeMode.THIS_WEEK) {
            timeFormat = `TO_CHAR(ct."createdDate", 'YYYY-MM-DD')`;
            startDate = now.startOf('week').toISOString(); // start from Monday
            for (let i = 0; i < 7; i++) {
                labels.push(now.startOf('week').add(i, 'day').format('YYYY-MM-DD'));
            }
        } else if (timeMode === SummaryByTimeMode.THIS_MONTH) {
            timeFormat = `TO_CHAR(ct."createdDate", 'YYYY-MM-DD')`;
            startDate = now.startOf('month').toISOString();
            const daysInMonth = now.daysInMonth();
            for (let i = 1; i <= daysInMonth; i++) {
                labels.push(now.date(i).format('YYYY-MM-DD'));
            }
        } else if (timeMode === SummaryByTimeMode.THIS_YEAR) {
            timeFormat = `TO_CHAR(ct."createdDate", 'YYYY-MM')`;
            startDate = now.startOf('year').toISOString();
            labels = Array.from({ length: 12 }, (_, i) => now.month(i).format('YYYY-MM'));
        }

        wheres.push(`ct."createdDate" >= '${startDate}'`);

        const sql = `
            SELECT 
            ${timeFormat} as "label",
            SUM(CASE WHEN ct."status" = '${NSChain.ETransactionStatus.COMPLETED}' THEN 1 ELSE 0 END) as "totalCompleted",
            SUM(CASE WHEN ct."status" = '${NSChain.ETransactionStatus.FAILED}' THEN 1 ELSE 0 END) as "totalFailed"
            FROM chain_transaction ct
            WHERE ${wheres.join(' AND ')}
            GROUP BY "label"
            ORDER BY "label"
        `;

        const result = await this.chainTransactionRepo.query(sql);

        const map = new Map<string, { totalCompleted: number; totalFailed: number }>();
        for (const row of result) {
            map.set(row.label, {
                totalCompleted: Number(row.totalCompleted),
                totalFailed: Number(row.totalFailed),
            });
        }

        const completedData = [];
        const failedData = [];

        for (const label of labels) {
            const data = map.get(label) || { totalCompleted: 0, totalFailed: 0 };
            completedData.push(data.totalCompleted);
            failedData.push(data.totalFailed);
        }

        return {
            totalRequest:
                completedData.reduce((a, b) => a + b, 0) + failedData.reduce((a, b) => a + b, 0),
            totalRequestSuccess: completedData.reduce((a, b) => a + b, 0),
            totalRequestFail: failedData.reduce((a, b) => a + b, 0),
            labels: labels.map(label => {
                const arr = label.split('-');
                return arr.pop();
            }),
            datasets: [
                {
                    label: 'Success',
                    data: completedData,
                    borderColor: '#6366f1',
                    backgroundColor: 'rgba(70, 241, 57, 0.2)',
                },
                {
                    label: 'Failed',
                    data: failedData,
                    borderColor: '#f43f5e',
                    backgroundColor: 'rgba(238, 24, 60, 0.2)',
                },
            ],
        };
    }

    logRequest(body: LogRequestReq) {
        return this.logRequestRepo.findPagination({}, body);
    }
}
