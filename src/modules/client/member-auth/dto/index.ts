import { PageRequest } from '~/@systems/utils';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import * as jwt from 'jsonwebtoken';

export class JwtPayload implements jwt.JwtPayload {
  @ApiPropertyOptional()
  iss?: string | undefined;
  @ApiPropertyOptional()
  sub?: string | undefined;
  @ApiPropertyOptional()
  aud?: string | string[] | undefined;
  @ApiPropertyOptional()
  exp?: number | undefined;
  @ApiPropertyOptional()
  nbf?: number | undefined;
  @ApiPropertyOptional()
  iat?: number | undefined;
  @ApiPropertyOptional()
  jti?: string | undefined;
}
export class MemberLoginReq extends JwtPayload {
  @ApiProperty()
  @IsNotEmpty()
  email: string;

  @ApiProperty()
  @IsNotEmpty()
  password: string;
}

export class MemberRegisterReq {
  @ApiProperty()
  @IsNotEmpty()
  email: string;

  @ApiPropertyOptional()
  fullName: string;

  @ApiProperty()
  @IsNotEmpty()
  password: string;
}

export class MemberSessionDto extends MemberLoginReq {
  @ApiProperty()
  accessToken: string;
  @ApiProperty()
  refreshToken: string;
  @ApiProperty()
  tokenType: 'Bearer' = 'Bearer';
}

export class MemberUpdateInfoDto {
  @ApiPropertyOptional()
  fullName: string;

  @ApiPropertyOptional()
  avatar: string;

  @ApiPropertyOptional()
  phone: string;
}
