import { generateCurlText } from '~/common/helpers/generate-text.helper';
import { configEnv } from '~/@config/env';
import { ApiDocumentInput } from './types';
import { NSChain } from '~/common/enums/chain.enum';

export default ({ configDetail, configApi, memberAccessToken }: ApiDocumentInput) => {
    const samplePayload = {
        pageSize: 10,
        pageIndex: 1,
        keyword: null,
        status: null,
        fromDate: null,
        toDate: null,
        configApiId: configApi.id,
    };
    const method = 'POST';
    const path = 'api/client/chain-transaction/list';
    const header = {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${memberAccessToken}`,
    };
    const host = configEnv().EXTERNAL_API_HOST;
    return {
        type: NSChain.ETransactionType.MAINNET,
        configApiId: configApi.id,
        host,
        path,
        method,
        header: JSON.stringify(header),
        body: samplePayload,
        curlText: generateCurlText(`${host}/${path}`.trim(), samplePayload, method, header),
        createdDate: configApi.createdDate,
        updatedDate: configApi.updatedDate,
    };
};
