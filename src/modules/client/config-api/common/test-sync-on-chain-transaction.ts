import { generatePayloadFields } from '~/common/helpers/generate-text.helper';
import { generateCurlText } from '~/common/helpers/generate-text.helper';
import { configEnv } from '~/@config/env';
import { ApiDocumentInput } from './types';
import { NSChain } from '~/common/enums/chain.enum';

export default ({ configDetail, apiKey, configApi }: ApiDocumentInput) => {
    const samplePayload = {
        configApiId: configApi.id,
        data: generatePayloadFields(configDetail),
    };
    const method = 'POST';
    const path = 'api/chain/test/sync-on-chain-transaction';
    const header = {
        'Content-Type': 'application/json',
        'x-api-key': apiKey,
    };
    const host = configEnv().EXTERNAL_API_HOST;
    return {
        type: NSChain.ETransactionType.TESTNET,
        configApiId: configApi.id,
        host,
        path,
        method,
        header: JSON.stringify(header),
        body: samplePayload,
        curlText: generateCurlText(`${host}/${path}`.trim(), samplePayload, method, header),
        createdDate: configApi.createdDate,
        updatedDate: configApi.updatedDate,
    };
};
