import { Injectable } from '@nestjs/common';
import { DefTransaction } from '~/@core/decorator';
import { configEnv } from '~/@config/env';
// import * as ethers from 'ethers';
import {
    Contract,
    JsonRpcProvider,
    TransactionReceipt,
    Wallet,
    keccak256,
    toUtf8Bytes,
    BigNumberish,
    formatUnits,
} from 'ethers';
import { SyncOnChainMultipleTransactionReq, SyncOnChainTransactionReq } from './dto/chain.dto';
import { BindRepo } from '~/@core/decorator';
import {
    ConfigApiRepo,
    ConfigApiDetailRepo,
    MemberApiLogRepo,
    MemberApiRepo,
    MemberKeyRepo,
    MemberPackageRepo,
    ChainTransactionRepo,
    ConfigApiEntity,
    MemberPackageEntity,
} from '~/domains/primary';
import { BusinessException } from '~/@systems/exceptions';
import { ConfigApiDetailEntity } from '~/domains/primary/config-api/config-api-detail.entity';
import { NSConfig, NSMember } from '~/common/enums';
import dayjs from 'dayjs';
import { NSChain } from '~/common/enums/chain.enum';

const { RPC_URL, WALLET_DEPLOYER_PRIVATEKEY, RECORD_HASH_STORAGE_ADDRESS, SCAN_URL } = configEnv();
const DEFAULT_FEE = 0.00001;
@Injectable()
export class ChainService {
    constructor() {}

    @BindRepo(ConfigApiRepo)
    private configApiRepo: ConfigApiRepo;

    @BindRepo(ConfigApiDetailRepo)
    private configApiDetailRepo: ConfigApiDetailRepo;

    @BindRepo(MemberKeyRepo)
    private memberKeyRepo: MemberKeyRepo;

    @BindRepo(MemberPackageRepo)
    private memberPackageRepo: MemberPackageRepo;

    @BindRepo(ChainTransactionRepo)
    private chainTransactionRepo: ChainTransactionRepo;

    @BindRepo(MemberApiRepo)
    private memberApiRepo: MemberApiRepo;

    @BindRepo(MemberApiLogRepo)
    private memberApiLogRepo: MemberApiLogRepo;

    private canonicalize(obj: Record<string, any>) {
        console.log(`=====OBJ=====`, obj);
        const sortedKeys = Object.keys(obj).sort();
        const sortedObj: Record<string, any> = {};
        for (const key of sortedKeys) {
            sortedObj[key] = obj[key];
        }
        return JSON.stringify(sortedObj);
    }
    private hashRecord(obj: Record<string, any>) {
        const str = this.canonicalize(obj);
        return keccak256(toUtf8Bytes(str));
    }

    hashRecords(objs: Record<string, any>[]) {
        return objs.map(obj => this.hashRecord(obj));
    }

    private calculateFee(tx: TransactionReceipt) {
        const fee = tx.gasUsed * tx.gasPrice;
        return formatUnits(fee, 18); // convert wei to BNB
    }

    async storeHash(body: SyncOnChainTransactionReq) {
        const provider = new JsonRpcProvider(RPC_URL);
        const wallet = new Wallet(WALLET_DEPLOYER_PRIVATEKEY, provider);
        const dataHash = this.hashRecord(body.data);
        const ct = new Contract(
            RECORD_HASH_STORAGE_ADDRESS,
            ['function storeHash(bytes32 dataHash, string configApiId) external'],
            provider,
        );
        const tx = await (
            await ct.connect(wallet)['storeHash(bytes32,string)'](dataHash, body.configApiId)
        ).wait();

        return {
            fee: this.calculateFee(tx),
            onchainData: dataHash,
            txHash: `${SCAN_URL}/tx/${tx.hash}`,
        };
    }

    async storeHashs(body: SyncOnChainMultipleTransactionReq) {
        const provider = new JsonRpcProvider(RPC_URL);
        const wallet = new Wallet(WALLET_DEPLOYER_PRIVATEKEY, provider);
        const dataHashs = this.hashRecords(body.data);
        const ct = new Contract(
            RECORD_HASH_STORAGE_ADDRESS,
            [
                'function storeHashs(bytes32[] calldata dataHashs, string calldata configApiId) external',
            ],
            provider,
        );
        const tx = await (
            await ct.connect(wallet)['storeHashs(bytes32[],string)'](dataHashs, body.configApiId)
        ).wait();

        return {
            fee: this.calculateFee(tx),
            onchainData: dataHashs,
            txHash: `${SCAN_URL}/tx/${tx.hash}`,
        };
    }

    private async validateData(
        data: Record<string, any> = {},
        configApiDetails: ConfigApiDetailEntity[],
    ) {
        const errors = [];
        for (const detail of configApiDetails) {
            const { configApiId, nameField, mappingField, type, isRequired, description } = detail;
            if (isRequired && !data[mappingField]) {
                errors.push({
                    mappingField,
                    message: `${nameField} [${mappingField}] is required`,
                });
            }
            if (type === NSConfig.ETypeField.TEXT && typeof data[mappingField] !== 'string') {
                errors.push({
                    mappingField,
                    message: `${nameField} [${mappingField}] must be a string`,
                });
            }
            if (type === NSConfig.ETypeField.NUMBER && typeof data[mappingField] !== 'number') {
                errors.push({
                    mappingField,
                    message: `${nameField} [${mappingField}] must be a number`,
                });
            }
            if (type === NSConfig.ETypeField.BOOLEAN && typeof data[mappingField] !== 'boolean') {
                errors.push({
                    mappingField,
                    message: `${nameField} [${mappingField}] must be a boolean`,
                });
            }
        }
        return errors;
    }

    async syncOnChainTransaction(
        body: SyncOnChainTransactionReq,
        transactionType: NSChain.ETransactionType = NSChain.ETransactionType.MAINNET,
    ) {
        const { configApiId, data } = body;
        const configApi = await this.configApiRepo.findOne({ where: { id: configApiId } });
        if (!configApi) {
            throw new BusinessException('chain.config_api.not_found');
        }

        const memberPackage = await this.memberPackageRepo.findOne({
            where: { memberId: configApi.memberId },
        });
        if (!memberPackage) {
            throw new BusinessException('chain.member_package.not_found');
        }
        if (memberPackage.status !== NSMember.EMemberPackageStatus.ACTIVE) {
            throw new BusinessException('chain.member_package.not_active');
        }
        const memberKey = await this.memberKeyRepo.findOne({
            where: { id: configApi.memberKeyId },
        });
        if (!memberKey) {
            throw new BusinessException('chain.member_key.not_found');
        }
        const configApiDetails = await this.configApiDetailRepo.find({
            where: { configApiId: configApi.id },
        });
        if (!configApiDetails?.length) {
            throw new BusinessException('chain.config_api.details_missing');
        }
        return this.writeOnChainTransaction(
            body,
            configApi,
            memberPackage,
            configApiDetails,
            data,
            transactionType,
        );
    }
    async writeOnChainTransaction(
        body: SyncOnChainTransactionReq,
        configApi: ConfigApiEntity,
        memberPackage: MemberPackageEntity,
        configApiDetails: ConfigApiDetailEntity[],
        data: Record<string, any>,
        transactionType: NSChain.ETransactionType = NSChain.ETransactionType.MAINNET,
    ) {
        const provider = new JsonRpcProvider(RPC_URL);
        const wallet = new Wallet(WALLET_DEPLOYER_PRIVATEKEY, provider);
        const byteSize = Buffer.byteLength(JSON.stringify(body?.data || {}));
        let onchainTransaction = await this.chainTransactionRepo.save({
            memberId: configApi.memberId,
            ownerAddress: wallet.address,
            configApiId: configApi.id,
            status: NSChain.ETransactionStatus.PENDING,
            dataOfChain: data || {},
            dataByteSize: byteSize,
            type: transactionType,
        });

        // Check kích thước byte của body

        if (byteSize > memberPackage.byteLimit) {
            onchainTransaction = await this.chainTransactionRepo.save({
                ...onchainTransaction,
                status: NSChain.ETransactionStatus.FAILED,
                errorMessage: 'Request body too large',
                errorDetail: JSON.stringify({
                    type: 'VALIDATION',
                    message: `Request body size over ${memberPackage.byteLimit} bytes`,
                }),
            });
            throw new BusinessException('chain.request_body_too_large', -1, {
                message: `Request body size over ${memberPackage.byteLimit} bytes`,
            });
        }

        const errors = await this.validateData(data, configApiDetails);
        if (errors.length) {
            onchainTransaction = await this.chainTransactionRepo.save({
                ...onchainTransaction,
                status: NSChain.ETransactionStatus.FAILED,
                errorMessage: 'Validation error',
                errorDetail: JSON.stringify({ type: 'VALIDATION', errors }),
            });
            throw new BusinessException('chain.config_api.details_not_match', -1, errors);
        }

        try {
            const { onchainData, txHash, fee } = await this.storeHash(body);
            onchainTransaction = await this.chainTransactionRepo.save({
                ...onchainTransaction,
                status: NSChain.ETransactionStatus.COMPLETED,
                txHash,
                dataOnChain: onchainData,
                fee: Number(fee),
            });
            return onchainTransaction;
        } catch (error) {
            onchainTransaction = await this.chainTransactionRepo.save({
                ...onchainTransaction,
                status: NSChain.ETransactionStatus.FAILED,
                errorMessage: 'Onchain error',
                errorDetail: JSON.stringify({ type: 'ONCHAIN', message: error.message }),
            });
            throw new BusinessException('chain.onchain_transaction.failed', -1, error.message);
        }
    }

    @DefTransaction()
    writeOnChainMultipleTransaction(body: SyncOnChainMultipleTransactionReq) {
        return this.storeHashs(body);
    }
}
