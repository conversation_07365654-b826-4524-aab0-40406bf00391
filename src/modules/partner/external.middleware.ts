import { Injectable, NestMiddleware, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request, Response } from 'express';
import { configEnv } from '~/@config/env';
import { RequestContext } from '~/@core/context';
import { KEY_HEADER } from '~/common/constants';
import { BindRepo } from '~/@core/decorator';
import { MemberKeyRepo } from '~/domains/primary';
import { NSMemberKey } from '~/common/enums';


@Injectable()
export class ExternalMiddleware implements NestMiddleware {
    constructor(
    ) { }

    @BindRepo(MemberKeyRepo)
    private memberKeyRepo: MemberKeyRepo;

    async use(req: Request, res: Response, next: Function) {
        console.log("=========== ExternalMiddleware ===========");
        try {
            if (req.path.startsWith('/api/partner/test')) {
               return next();
            }

            const { headers = {} } = req;
            if (!headers || !headers[KEY_HEADER.EXTERNAL_API_KEY]) {
                throw new UnauthorizedException('Unauthorized');
            }
            const apiKey = headers[KEY_HEADER.EXTERNAL_API_KEY] as string;
            const _apiKeyText = apiKey.trim();

            if (!apiKey) {
                throw new UnauthorizedException('Unauthorized');
            }

            // Kiểm tra apiKey match với member_key
            const memberKey = await this.memberKeyRepo.findOne({ where: { apiKey: _apiKeyText, status: NSMemberKey.EStatus.ACTIVE } });
            if (!memberKey) {
                throw new UnauthorizedException('Unauthorized');
            }

            try {
                next();
            } catch (error) {
                console.log(`==========`, error);
                throw new UnauthorizedException('Unauthorized');
            }
        } catch (error) {
            console.log(error);
            next(new UnauthorizedException('Unauthorized'));
        }
    }
}
