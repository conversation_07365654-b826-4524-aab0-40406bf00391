import { MiddlewareConsumer, NestModule, RequestMethod } from '@nestjs/common';
import { ChildModule } from '~/@core/decorator';
import { REFIX_MODULE } from '../config-module';
import { ExternalController } from './external/external.controller';
import { ExternalService } from './external/external.service';
import { ExternalMiddleware } from './external.middleware';

@ChildModule({
  prefix: REFIX_MODULE.partner,
  providers: [ ExternalService ],
  controllers: [ExternalController],
})
export class PartnerModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
    .apply(ExternalMiddleware)
    .forRoutes({
      path: `${REFIX_MODULE.partner}/*`,
      method: RequestMethod.ALL,
    });
    }
}
