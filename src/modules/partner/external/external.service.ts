import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { BindRepo, DefTransaction } from "~/@core/decorator";
import {
  ConfigApiRepo,
  ConfigApiDetailRepo,
  MemberApiRepo,
  MemberApiLogRepo,
  MemberPackageRepo,
  MemberKeyRepo,
  PaymentTransactionRepo,
} from "~/domains/primary";
import { NSConfig, NSMember, NSPayment } from "~/common/enums";
import * as dayjs from "dayjs";
import { ConfigApiDetailEntity } from "~/domains/primary/config-api/config-api-detail.entity";
import { ENDPOINT } from "~/common/constants/endpoint";
import { RequestExternalDto } from "./dto/external.dto";
import { configEnv } from "~/@config/env";
import { BusinessException } from "~/@systems/exceptions";
import { PackagePlanRepo } from "~/domains/primary/package/package.repo";

@Injectable()
export class ExternalService {
  constructor() { }

  @BindRepo(ConfigApiRepo)
  private configRepo: ConfigApiRepo;

  @BindRepo(ConfigApiDetailRepo)
  private detailRepo: ConfigApiDetailRepo;

  @BindRepo(MemberApiRepo)
  private memberApiRepo: MemberApiRepo;

  @BindRepo(MemberApiLogRepo)
  private memberApiLogRepo: MemberApiLogRepo;

  @BindRepo(MemberPackageRepo)
  private memberPackageRepo: MemberPackageRepo;

  @BindRepo(MemberKeyRepo)
  private memberKeyRepo: MemberKeyRepo;

  @BindRepo(PaymentTransactionRepo)
  private paymentTransactionRepo: PaymentTransactionRepo;

  @BindRepo(PackagePlanRepo)
  private packagePlanRepo: PackagePlanRepo;

  //#region Dispatch by endpoint
  async dispatchByEndpoint(params: {
    method: string;
    path: string;
    configApiId: string;
    projectId?: string;
    body: any;
    headers: any;
  }) {
    const { method, path, body, headers, configApiId, projectId } = params;
    const apiKey = headers['x-api-key'];
    const endpoint = ENDPOINT.EXTERNAL_API.find(
      (e) => (e.path ?? '') === (path ?? '') && e.method === method.toUpperCase()
    );

    if (!endpoint) {
      throw new NotFoundException(`Endpoint ${method} ${path} not found`);
    }

    const host = configEnv().EXTERNAL_API_HOST;
    const url = `${host}${path}`;

    // Dispatch tới Handler logic tương ứng
    switch (endpoint.path) {
      case '/api/partner/config/info':
        return await this.handleConfigInfo(body, configApiId, host, url, method);
      case '/api/partner/config/logs':
        return await this.handleConfigLog(body, configApiId, host, url, method);
      case '/api/partner/project/info':
        return await this.handleProjectInfo(body, projectId, host, url, method, apiKey);
      case '/api/partner/my-plan':
        return await this.handleMyPlan(body, apiKey, host, url, method);
      case '/api/partner/payment':
        return await this.handlePaymentTransaction(body, apiKey, host, url, method);
    }

    throw new NotFoundException('Path is not found'); // 404
  }
  //#endregion

  //#region Handler Logic
  @DefTransaction()
  async handleConfigInfo(body: any, configApiId: string, host: string, url: string, method: string) {
    const config = await this.configRepo.findOne(configApiId);
    if (!config) throw new BusinessException('external.config_package_not_found');
    const memberAPI = await this.memberApiRepo.findOne({
      where:
      {
        configApiId: configApiId,
        method: NSConfig.EApiMethod.GET,
        code: 'external_api_config_info',
      }
    });

    if (!memberAPI) throw new BusinessException('external.member_api_not_found');

    const memberPackage = await this.memberPackageRepo.findOne({
      where: { memberId: config.memberId },
    });

    // Ghi logs
    await this.saveApiLog({
      config,
      memberId: config.memberId,
      memberPackage: memberPackage,
      body,
      response: { message: "success" },
      isTest: false,
      statusCode: NSConfig.EApiStatusCode.SUCCESS,
      host,
      url,
      method,
    });

    if (!config) throw new BusinessException("external.config_package_not_found");
    const details = await this.detailRepo.find({ where: { configApiId: config.id } });

    const apiInfo = await this.memberApiRepo.findOne({ where: { configApiId: config.id } });

    delete apiInfo.header;
    delete apiInfo.curlText;

    return {
      ...config,
      apiInfo,
      fields: details || [],
    };
  }

  @DefTransaction()
  async handleConfigLog(body: any, configApiId: string, host: string, url: string, method: string) {
    const config = await this.configRepo.findOne(configApiId);
    if (!config) throw new BusinessException("external.config_package_not_found");

    const memberAPI = await this.memberApiRepo.findOne({
      where: {
        configApiId: config.id,
        method: NSConfig.EApiMethod.GET,
        code: 'external_api_config_log',
      }
    });
    if (!memberAPI) throw new BusinessException("external.member_api_not_found");

    const { pageIndex = 1, pageSize = 10 } = body;

    const memberPackage = await this.memberPackageRepo.findOne({
      where: { memberId: config.memberId },
    });

    // Ghi log
    await this.saveApiLog({
      config,
      memberId: config.memberId,
      memberPackage,
      body,
      response: { message: "success" },
      isTest: false,
      statusCode: NSConfig.EApiStatusCode.SUCCESS,
      host,
      url,
      method,
    });

    const logs = await this.memberApiLogRepo.findPagination({
      where: {
        configApiId: config.id,
        isTest: false,
      },
      order: { createdDate: 'DESC' },
    }, { pageIndex, pageSize });
    return logs;
  }

  // Lấy thông tin project
  @DefTransaction()
  async handleProjectInfo(body: any, projectId: string, host: string, url: string, method: string, apiKey: string) {
    const config = await this.configRepo.find({
      where: {
        memberKeyId: projectId
      },
    });
    if (!config) throw new BusinessException("external.config_package_not_found");

    const memberKey = await this.memberKeyRepo.findOne({ where: { id: projectId } });
    if (!memberKey) throw new BusinessException("external.member_key_not_found");

    const memberPackage = await this.memberPackageRepo.findOne({
      where: { memberId: memberKey.memberId },
    });
    if (!memberPackage) throw new BusinessException("external.member_package_not_found");

    // Ghi log
    await this.saveApiLog({
      config,
      memberId: memberKey.memberId,
      memberPackage,
      body,
      response: { message: "success" },
      isTest: false,
      statusCode: NSConfig.EApiStatusCode.SUCCESS,
      host,
      url,
      method,
    });

    delete memberKey.apiKey;
    delete memberKey.publicKey;

    return {
      project: memberKey,
      configs: [config],
    };
  }

  @DefTransaction()
  async handleMyPlan(body: RequestExternalDto, apiKey: string, host: string, url: string, method: string) {
    const memberKey = await this.memberKeyRepo.findOne({ where: { apiKey } });
    if (!memberKey) throw new BusinessException("external.member_key_not_found");

    const memberPackage = await this.memberPackageRepo.findOne({
      where: { memberId: memberKey.memberId },
    });
    if (!memberPackage) throw new BusinessException("external.member_package_not_found");

    // Lấy thông tin của gói đang dùng
    const packagePlan = await this.packagePlanRepo.findOne({ where: { id: memberPackage.packagePlanId } });
    if (!packagePlan) throw new BusinessException("external.package_plan_not_found");

    // Save log
    await this.saveApiLog({
      config: null,
      memberId: memberKey.memberId,
      memberPackage,
      body,
      response: { message: "success" },
      isTest: false,
      statusCode: NSConfig.EApiStatusCode.SUCCESS,
      host,
      url,
      method,
    });

    return {
      ...memberPackage,
      namePackage: packagePlan.name,
    };
  }

  @DefTransaction()
  async handlePaymentTransaction(body: RequestExternalDto, apiKey: string, host: string, url: string, method: string) {
    const memberKey = await this.memberKeyRepo.findOne({ where: { apiKey } });
    if (!memberKey) throw new BusinessException("external.member_key_not_found");

    const memberPackage = await this.memberPackageRepo.findOne({
      where: { memberId: memberKey.memberId },
    });
    if (!memberPackage) throw new BusinessException("external.member_package_not_found");

    const paymentTransactions = await this.paymentTransactionRepo.find({
      where: {
        memberId: memberKey.memberId,
        status: NSPayment.ETransactionStatus.COMPLETED,
      },
    });

    // Save log
    await this.saveApiLog({
      config: null,
      memberId: memberKey.memberId,
      memberPackage,
      body,
      response: { message: "success" },
      isTest: false,
      statusCode: NSConfig.EApiStatusCode.SUCCESS,
      host,
      url,
      method,
    });

    return paymentTransactions;
  }
  //#endregion

  //#region Helper
  // private async validateOrHandleIncomingData(body: any, details: ConfigApiDetailEntity[]) {
  //   const result: Record<string, any> = {};

  //   for (const field of details) {
  //     const value = body[field.mappingField];

  //     if (field.isRequired && (value === undefined || value === null)) {
  //       return {
  //         statusCode: 400,
  //         isValid: false,
  //         message: `Missing required field: ${field.mappingField}`,
  //         data: null,
  //       };
  //     }

  //     const type = field.type.toLowerCase();

  //     switch (type) {
  //       case "string":
  //         if (typeof value !== "string") {
  //           return {
  //             statusCode: 400,
  //             isValid: false,
  //             message: `${field.mappingField} must be a string`,
  //             data: null,
  //           };
  //         }
  //         result[field.nameField] = value;
  //         break;

  //       case "number":
  //         if (typeof value !== "number") {
  //           return {
  //             statusCode: 400,
  //             isValid: false,
  //             message: `${field.mappingField} must be a number`,
  //             data: null,
  //           };
  //         }
  //         result[field.nameField] = value;
  //         break;

  //       case "boolean":
  //         if (typeof value !== "boolean") {
  //           return {
  //             statusCode: 400,
  //             isValid: false,
  //             message: `${field.mappingField} must be a boolean`,
  //             data: null,
  //           };
  //         }
  //         result[field.nameField] = value;
  //         break;

  //       case "json":
  //         try {
  //           const parsed = typeof value === "object" ? value : JSON.parse(value);
  //           result[field.nameField] = parsed;
  //         } catch (e) {
  //           return {
  //             statusCode: 400,
  //             isValid: false,
  //             message: `${field.mappingField} must be valid JSON`,
  //             data: null,
  //           };
  //         }
  //         break;

  //       case "date":
  //         if (!dayjs(value).isValid()) {
  //           return {
  //             statusCode: 400,
  //             isValid: false,
  //             message: `${field.mappingField} must be a valid date`,
  //             data: null,
  //           };
  //         }
  //         result[field.nameField] = dayjs(value).toISOString();
  //         break;

  //       default:
  //         return {
  //           statusCode: 400,
  //           isValid: false,
  //           message: `Unsupported type: ${field.type}`,
  //           data: null,
  //         };
  //     }
  //   }

  //   return {
  //     statusCode: 200,
  //     isValid: true,
  //     message: "Validation successful",
  //     data: result,
  //   };
  // }

  private async saveApiLog({
    config,
    memberId,
    memberPackage,
    body,
    response,
    isTest,
    statusCode,
    host,
    url,
    method,
  }: {
    config: any;
    memberId: any;
    memberPackage: any;
    body: any;
    response: any;
    isTest: boolean;
    statusCode: NSConfig.EApiStatusCode;
    host: string;
    url: string;
    method: string;
  }) {
    const log = this.memberApiLogRepo.create({
      configApiId: config?.id,
      memberId: memberId,
      memberPackageId: memberPackage.id,
      host,
      url,
      request: body,
      response,
      isTest,
      method,
      statusCode,
    });
    await this.memberApiLogRepo.save(log);
  }
  //#endregion
}
