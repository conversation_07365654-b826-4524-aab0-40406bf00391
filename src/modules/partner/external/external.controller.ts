import { All, Body, Req } from '@nestjs/common';
import { ExternalService } from './external.service';
import { Request } from 'express';
import { DefController } from '~/@core/decorator';
import { Throttle } from '@nestjs/throttler';

@DefController('')
export class ExternalController {
  constructor(private readonly service: ExternalService) { }

  @Throttle({ default: { limit: 10, ttl: 60000 } }) // 1 phút 10 request
  @All('*')
  async handleDynamicRoute(
    @Req() req: Request,
    @Body() body: any
  ) {
    const method = req.method;
    const path = req.path;

    return this.service.dispatchByEndpoint({
      method,
      path,
      configApiId: body.configApiId,
      projectId: body.projectId,
      body,
      headers: req.headers,
    });
  }
}
