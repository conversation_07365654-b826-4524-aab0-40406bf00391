import { MiddlewareConsumer, NestModule } from '@nestjs/common';
import { ChildModule } from '~/@core/decorator';
import { REFIX_MODULE } from '../config-module';
import { StripeService } from './web-hook/stripe.service';
import { <PERSON>eController } from './web-hook/stripe.controller';

@ChildModule({
  prefix: REFIX_MODULE.integration,
  providers: [StripeService],
  controllers: [StripeController],
})
export class IntegrationModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // consumer
    // .apply(ExternalMiddleware)
    // .forRoutes({
    //   path: `${REFIX_MODULE.integration}/external/*`,
    //   method: RequestMethod.ALL,
    // });

    // consumer
    //   .apply(MemberMiddleware)
    //   .forRoutes({
    //     path: `${REFIX_MODULE.integration}/external/test/*`,
    //     method: RequestMethod.ALL,
    //   });
    }
}
