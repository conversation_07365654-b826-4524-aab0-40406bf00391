import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsUUID } from 'class-validator';
import { NSPayment } from '~/common/enums';
import { NSOrder } from '~/common/enums/order.enum';
import { NSPackage } from '~/common/enums/package.enum';

export class StripeWebhookDto {
  @ApiProperty({ description: 'ID của đơn hàng' })
  @IsUUID()
  orderId: string;

  @ApiProperty({ description: 'ID của giao dịch' })
  @IsUUID()
  paymentTransactionId: string;

  @ApiProperty({ description: 'Trạng thái thanh toán' })
  @IsEnum(NSOrder.EPaymentStatus)
  paymentStatus: NSOrder.EPaymentStatus;

  @ApiProperty({ description: 'Loại gói dịch vụ' })
  @IsEnum(NSPackage.EPlanTypePayment)
  paymentType: string;

  @ApiProperty({ description: 'ID của gói dịch vụ' })
  @IsUUID()
  memberPackageId: string;

  @ApiPropertyOptional({ description: 'ID của subscription' })
  @IsOptional()
  subscriptionId?: string;

  @ApiProperty({ description: 'Thời gian đăng ký (tháng/năm)' })
  timeRegister?: string;

  @ApiPropertyOptional({ description: 'ID của hóa đơn' })
  @IsOptional()
  invoiceId?: string;

  @IsOptional()
  @ApiPropertyOptional({ description: 'Đối tác cung cấp invoice' })
  invoiceProvider?: NSPayment.EPaymentProvider;
}
