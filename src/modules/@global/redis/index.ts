import { DynamicModule, Global, Module } from '@nestjs/common';
import { createClient, RedisClientType } from 'redis';
import { RedisService } from './redis.service';
import { REDIS_PREFIX, DEFAULT_REDIS_NAMESPACE } from './redis.constants';

export interface RedisModuleOptions {
  username?: string;
  password?: string;
  socket: {
    host: string;
    port: number;
  };
  db?: number;
}

@Global()
@Module({})
export class RedisModule {
  static forRoot(
    config: RedisModuleOptions,
    name: string = DEFAULT_REDIS_NAMESPACE,
  ): DynamicModule {
    const token = `${REDIS_PREFIX}${name}`;

    const clientProvider = {
      provide: token,
      useFactory: async () => {
        const client: RedisClientType = createClient({
          username: config.username,
          password: config.password,
          socket: config.socket,
          database: config.db,
        });

        client.on('connect', () => console.log(`✅ Redis (${name}) connected`));
        client.on('error', err => console.error(`❌ Redis (${name}) error`, err));

        await client.connect();
        return new RedisService(client);
      },
    };

    return {
      module: RedisModule,
      providers: [clientProvider],
      exports: [clientProvider],
    };
  }
}
