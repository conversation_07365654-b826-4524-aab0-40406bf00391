import { RedisClientType } from 'redis';

export class RedisService {
  constructor(private readonly client: RedisClientType) {}

  async get<T = any>(key: string): Promise<T | null> {
    const value = await this.client.get(key);
    if (!value) return null;
    try {
      return JSON.parse(value as string) as T;
    } catch {
      return value as T;
    }
  }

  async set(key: string, value: any, ttl = 3600): Promise<void> {
    const str = typeof value === 'string' ? value : JSON.stringify(value);
    await this.client.set(key, str, { EX: ttl });
  }

  async setnx(key: string, value: any, ttl = 3600): Promise<boolean> {
    const str = typeof value === 'string' ? value : JSON.stringify(value);
    const res = await this.client.set(key, str, { NX: true, EX: ttl });
    return res === 'OK';
  }

  async hgetall(key: string): Promise<Record<string, string>> {
    return await this.client.hGetAll(key);
  }

  async hmset(key: string, data: Record<string, string>, ttl = 3600): Promise<void> {
    await this.client.hSet(key, data);
    await this.client.expire(key, ttl);
  }

  async incr(key: string): Promise<number> {
    return await this.client.incr(key);
  }

  async expire(key: string, ttl: number): Promise<void> {
    await this.client.expire(key, ttl);
  }

  async del(key: string): Promise<void> {
    await this.client.del(key);
  }

  async ttl(key: string): Promise<number> {
    return await this.client.ttl(key);
  }

  async flushAll(): Promise<void> {
    await this.client.flushAll();
  }

  async getAndClearKey<T = any>(key: string): Promise<T | null> {
    const value = await this.get(key);
    await this.del(key);
    return value;
  }

  async disconnect(): Promise<void> {
    await this.client.quit();
  }
}
