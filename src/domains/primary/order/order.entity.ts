import { PrimaryBaseEntity } from "../primary-base.entity";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Column, Entity, Index } from "typeorm";
import { NSPayment } from "~/common/enums";
import { NSOrder } from "~/common/enums/order.enum";

@Entity('order')
export class OrderEntity extends PrimaryBaseEntity {
    @ApiProperty({ description: 'ID của thành viên' })
    @Column({ type: 'uuid' })
    @Index()
    memberId: string;

    @ApiProperty({ description: 'Mã đơn hàng', example: 'ODR-xxxxxx-uuid-nanoid-xxxx' })
    @Column({ type: 'varchar', length: 255 })
    orderCode: string;

    // Loại đơn hàng
    @Column({ type: 'varchar', length: 20, default: NSOrder.EOrderType.PACKAGE })
    orderType: string;

    @ApiProperty({ description: '<PERSON><PERSON><PERSON> b<PERSON>' })
    @Column({ type: 'numeric', precision: 15, scale: 2, default: 0 })
    totalPrice: number;

    @ApiProperty({ description: 'Tổng giá trị đơn hàng (bao gồm VAT)' })
    @Column({ type: 'numeric', precision: 15, scale: 2, default: 0 })
    totalPriceVat: number;

    @ApiProperty({ description: 'VAT' })
    @Column({ type: 'numeric', precision: 15, scale: 2, default: 0 })
    vat: number;

    @ApiProperty({
        description: 'Phương thức thanh toán (nếu có)',
        enum: NSOrder.EPaymentMethod,
        example: NSOrder.EPaymentMethod.PAY_CARD,
    })
    @Column({ type: 'varchar', length: 255, default: NSOrder.EPaymentMethod.PAY_CARD })
    paymentMethod: NSOrder.EPaymentMethod;

    @ApiPropertyOptional({
        description: 'Trạng thái thanh toán',
        enum: NSOrder.EPaymentStatus,
        example: NSOrder.EPaymentStatus.PAID,
    })
    @Column({ type: 'varchar', nullable: true })
    paymentStatus: NSOrder.EPaymentStatus;

    @ApiPropertyOptional({ description: 'Ngày thanh toán' })
    @Column({ type: 'timestamptz', nullable: true })
    paymentDate?: Date;

    @ApiProperty({ description: 'Trạng thái' })
    @Column({ type: 'varchar', length: 20, default: NSOrder.EStatus.PENDING })
    status: NSOrder.EStatus;

    @ApiPropertyOptional({ description: 'ID của invoice' })
    @Column({ type: 'varchar', nullable: true })
    invoiceId: string;

    @ApiPropertyOptional({ description: 'Đối tác cung cấp invoice' })
    @Column({ type: 'varchar', nullable: true })
    invoiceProvider: NSPayment.EPaymentProvider;

    @ApiPropertyOptional({ description: 'Ngày bắt đầu kỳ (áp dụng cho đơn hàng gia hạn)' })
    @Column({ type: 'timestamptz', nullable: true })
    periodStart: Date;

    @ApiPropertyOptional({ description: 'Ngày kết thúc kỳ (áp dụng cho đơn hàng gia hạn)' })
    @Column({ type: 'timestamptz', nullable: true })
    periodEnd: Date;
}
