// src/entities/news-article.entity.ts
import { Entity, Column, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';
import { NSNews } from '~/common/enums/news.enum';

@Entity('news_article')
@Index(['slug'], { unique: true })
export class NewsArticle extends PrimaryBaseEntity {
    @Column({ type: 'varchar', length: 255 })
    @ApiProperty({ description: 'Tiêu đề bài viết' })
    title: string;

    @Column({ type: 'text' })
    @ApiProperty({ description: 'Nội dung HTML (từ React Quill)' })
    contentHtml: string;

    @Column({ type: 'varchar', length: 512, nullable: true })
    @ApiProperty({ description: 'Mô tả ngắn', required: false })
    shortDescription?: string;

    @Column({ type: 'varchar', length: 512, nullable: true })
    @ApiProperty({ description: 'Ảnh thumbnail URL', required: false })
    thumbnailUrl?: string;

    @Column({ type: 'varchar', length: 255, unique: true })
    @ApiProperty({ description: 'Slug URL để SEO' })
    slug: string;

    @Column({
        type: 'varchar',
        default: NSNews.EStatus.DRAFT,
    })
    @ApiProperty({ enum: NSNews.EStatus, description: 'Trạng thái bài viết' })
    status: NSNews.EStatus;

    @Column({ type: 'timestamptz', nullable: true })
    @ApiProperty({ description: 'Thời gian đăng bài', required: false })
    publishedAt?: Date;
}
