import { Entity, Column, Index } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';
import { NSDocument } from '~/common/enums/document.enum';

// <PERSON>hi thông tin các api cần show document cho người dùng
@Entity('log_request')
export class LogRequestEntity extends PrimaryBaseEntity {
    @ApiPropertyOptional()
    @Column({ type: 'varchar', length: 10, nullable: true })
    method?: string;

    @ApiPropertyOptional()
    @Column({ type: 'varchar', length: 255, nullable: true })
    url?: string;

    @ApiPropertyOptional()
    @Column({ type: 'jsonb', nullable: true })
    headers?: any;

    @ApiPropertyOptional()
    @Column({ type: 'jsonb', nullable: true })
    body?: any;

    @ApiPropertyOptional()
    @Column({ type: 'varchar', length: 45, nullable: true })
    ip?: string;

    @ApiPropertyOptional()
    @Column({ type: 'int', nullable: true })
    statusCode?: number;

    @ApiPropertyOptional()
    @Column({ type: 'int', nullable: true })
    responseTime?: number;

    @ApiPropertyOptional()
    @Column({ type: 'jsonb', nullable: true })
    response?: any;
}
