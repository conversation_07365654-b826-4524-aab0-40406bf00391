import { Column, Entity, Index } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';
import { NSPackage } from '~/common/enums/package.enum';

@Entity('package_plan')
@Index(['code'], { unique: true })
export class PackagePlanEntity extends PrimaryBaseEntity {
  @ApiProperty({ description: 'Mã gói dịch vụ' })
  @Column({ type: 'varchar', length: 255 })
  code: string;

  @ApiProperty({ description: 'Tên gói dịch vụ' })
  @Column({ type: 'varchar', length: 255 })
  name: string;

  @ApiProperty({ description: '<PERSON><PERSON><PERSON> gốc (Đơn giá theo tháng)' })
  @Column({ type: 'numeric', precision: 15, scale: 2, default: 0 })
  originalPrice: number;

  @ApiPropertyOptional({ description: '<PERSON><PERSON> chú / <PERSON><PERSON><PERSON> tả chung cho gói' })
  @Column({ type: 'varchar', nullable: true })
  note?: string;

  @ApiPropertyOptional({ description: '<PERSON><PERSON> phổ biến nhất' })
  @Column({ type: 'boolean', default: false })
  isMostPopular?: boolean;

  @ApiPropertyOptional({ description: 'Trạng thái' })
  @Column({ type: 'varchar', length: 20, default: NSPackage.EStatus.ACTIVE })
  status?: NSPackage.EStatus;

  @ApiPropertyOptional({ description: 'Thứ tự hiển thị trong danh sách (gói nào lên trước)' })
  @Column({ type: 'int', default: 0 })
  displayOrder?: number;

  @ApiPropertyOptional({ description: 'Gói ẩn (không public trên UI)' })
  @Column({ type: 'boolean', default: false })
  isHidden?: boolean;
}
