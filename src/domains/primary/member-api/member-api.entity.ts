import { Entity, Column, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

// Danh sách các API của member
@Entity('member_api')
export class MemberApiEntity extends PrimaryBaseEntity {
    @ApiProperty({
        description: 'Mã API (dùng cho truy vấn nhanh)',
    })
    @Column({ type: 'varchar', length: 255, nullable: true })
    @Index()
    code: string;

    @ApiProperty({
        description: 'ID của thành viên',
    })
    @Column({ type: 'uuid' })
    @Index()
    memberId: string;

    @ApiProperty({
        description: 'ID của member key (project)',
    })
    @Column({ type: 'uuid', nullable: true })
    @Index()
    memberKeyId: string;

    @ApiProperty({
        description: 'ID của cấu hình Member API',
    })
    @Column({ type: 'uuid' })
    @Index()
    configApiId: string;

    @ApiProperty({ description: 'ID của gói dịch vụ áp dụng cho cấu hình API này' })
    @Column({ type: 'uuid', nullable: true })
    @Index()
    packagePlanId: string;

    @ApiProperty({ description: 'Host của API' })
    @Column({ type: 'varchar', length: 255 })
    host: string;

    @ApiProperty({ description: 'Path của API' })
    @Column({ type: 'varchar', length: 255 })
    path: string;

    @ApiProperty({ description: 'Phương thức của API' })
    @Column({ type: 'varchar', length: 10, default: 'POST' })
    method: string = 'POST';

    @ApiProperty({ description: 'Body của API' })
    @Column({ type: 'jsonb', nullable: true })
    body: any;

    @ApiProperty({ description: 'Header của API' })
    @Column({ type: 'jsonb', nullable: true })
    header: string;

    @ApiProperty({ description: 'Câu lệnh CURL' })
    @Column({ type: 'text' })
    curlText: string;
}
