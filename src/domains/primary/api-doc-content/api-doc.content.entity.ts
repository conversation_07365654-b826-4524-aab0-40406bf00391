import { Column, Entity } from "typeorm";
import { PrimaryBaseEntity } from "../primary-base.entity";
import { NSConfig } from "~/common/enums/config.enum";
import { ApiProperty } from "@nestjs/swagger";

@Entity('api_doc_content')
export class ApiDocContentEntity extends PrimaryBaseEntity {
  @Column({ unique: true, nullable: true })
  @ApiProperty({ description: 'Slug URL' })
  slug: string; // e.g. 'introduction', 'setup-project'

  @ApiProperty({ description: 'Ngôn ngữ', enum: NSConfig.EApiLDocumentLang })
  @Column({ default: NSConfig.EApiLDocumentLang.VNI })
  locale: string;

  @ApiProperty({ description: 'Tiêu đề' })
  @Column()
  title: string;

  @ApiProperty({ description: 'Nội dung dạng HTML' })
  @Column({ type: 'text' })
  content: string; // HTML from React Quill

  @ApiProperty({ description: 'Thứ tự hiển thị' })
  @Column({ type: 'int', default: 0 })
  sort: number
}
