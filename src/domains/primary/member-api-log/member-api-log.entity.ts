import { Entity, Column, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';
import { NSConfig } from '~/common/enums';

@Entity('member_api_log')
export class MemberApiLogEntity extends PrimaryBaseEntity {
    @ApiProperty({ description: 'ID của thành viên' })
    @Column({ type: 'uuid' })
    @Index()
    memberId: string;

    @ApiProperty({ description: 'ID của gói dịch vụ member đã mua' })
    @Column({ type: 'uuid' })
    @Index()
    memberPackageId: string;

    @ApiProperty({ description: 'ID của cấu hình API' })
    @Column({ type: 'uuid', nullable: true })
    @Index()
    configApiId: string;

    @ApiProperty({ description: 'Host của API' })
    @Column({ type: 'varchar', length: 255 })
    host: string;

    @ApiProperty({description: 'URL của API' })
    @Column({ type: 'varchar', length: 255 })
    url: string;

    @ApiProperty({ description: 'Thông tin request của API' })
    @Column({ type: 'jsonb', nullable: true })
    request: any;

    @ApiProperty({ description: 'Thông tin response của API' })
    @Column({ type: 'jsonb', nullable: true })
    response: any;

    @ApiProperty({ description: 'Là request test hay không' })
    @Column({ type: 'boolean', default: false })
    isTest: boolean;

    @ApiProperty({ description: 'Phương thức của API' })
    @Column({ type: 'varchar', length: 10, default: 'POST' })
    method: string;

    @ApiProperty({ description: 'Trạng thái của request' })
    @Column({ type: 'int', default: 200 })
    statusCode: number;
}
