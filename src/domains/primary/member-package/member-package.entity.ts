import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity, Index } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { NSPayment } from '~/common/enums/payment.enum';
import { NSPackage } from '~/common/enums';

/**
 * Hết hạn, không dùng xài hết thì bắt buộc mua lại gói khác
 */
@Entity('member_package')
export class MemberPackageEntity extends PrimaryBaseEntity {
  @ApiProperty({ description: 'ID của thành viên' })
  @Column({ type: 'uuid' })
  @Index()
  memberId: string;

  @ApiProperty({ description: 'ID của gói dịch vụ' })
  @Column({ type: 'uuid' })
  @Index()
  packagePlanId: string;

  @ApiProperty({ description: 'Ngày hết hạn' })
  @Column({ type: 'timestamptz', nullable: true })
  expiredDate: Date;

  @ApiProperty({ description: '<PERSON><PERSON>y kích hoạt' })
  @Column({ type: 'timestamptz', nullable: true })
  activatedDate: Date;

  // Ngày thanh toán tiếp theo
  @ApiProperty({ description: 'Ngày thanh toán tiếp theo' })
  @Column({ type: 'timestamptz', nullable: true })
  nextPaymentDate: Date;

  @ApiProperty({ description: 'Số lượng giao dịch được phép' })
  @Column({ type: 'int' })
  initialTransactionLimit: number;

  @ApiProperty({ description: 'Số lượng giao dịch hiện tại' })
  @Column({ type: 'int' })
  currentTransaction: number;

  @ApiProperty({ description: 'Số lượng cấu hình được phép' })
  @Column({ type: 'int' })
  initialConfigLimit: number;

  @ApiProperty({ description: 'Số lượng cấu hình hiện tại' })
  @Column({ type: 'int' })
  currentConfig: number;

  @ApiProperty({ description: 'Số lượng project (member-key) được phép sử dụng', nullable: true })
  @Column({ type: 'int', default: 0 })
  projectLimit: number;

  @Column({ type: 'int', default: 0, nullable: true })
  currentProject: number;

  @ApiProperty({ description: 'Trạng thái' })
  @Column({ type: 'varchar', length: 20, default: 'PENDING' })
  status: string;

  @ApiProperty({ description: 'Là gói dịch vụ tự động gia hạn' })
  @Column({ type: 'boolean', default: false })
  isAutoRenewal: boolean;

  @ApiProperty({ description: 'ID của subscription' })
  @Column({ type: 'varchar', nullable: true })
  subscriptionId: string;

  @ApiProperty({ description: 'Đối tác cung cấp subscription' })
  @Column({ type: 'varchar', nullable: true })
  subscriptionProvider: NSPayment.EPaymentProvider;

  @ApiProperty({ description: 'Là gói dùng thử' })
  @Column({ type: 'boolean', default: false })
  isTrial: boolean;

  // Giá lúc mua
  @ApiProperty({ description: 'Giá gói dịch vụ lúc mua' })
  @Column({ type: 'numeric', precision: 20, scale: 2, default: 0 })
  buyPrice: number;

  @ApiProperty({ description: 'Thời gian đăng ký (tháng/năm)' })
  @Column({ type: 'int', default: 1 })
  timeRegister: number;

  @ApiProperty({ description: 'Số lượng byte được phép (tháng)' })
  @Column({ type: 'int', nullable: true })
  byteLimit: number;
}
