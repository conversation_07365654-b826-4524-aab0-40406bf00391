import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { PrimaryBaseEntity } from "../primary-base.entity";
import { Column, Entity, Index } from "typeorm";
import { NSConfig } from "~/common/enums/config.enum";

// Danh sách cấu hình của member
@Entity('config_api')
export class ConfigApiEntity extends PrimaryBaseEntity {
    @ApiProperty({ description: 'ID thành viên' })
    @Column({ type: 'uuid' })
    @Index()
    memberId: string;

    @ApiProperty({ description: 'ID của member key' })
    @Column({ type: 'uuid' })
    @Index()    
    memberKeyId: string;

    @ApiProperty({ description: 'Mã cấu hình (dùng cho truy vấn nhanh)' })
    @Column({ type: 'varchar', length: 100, nullable: true })
    @Index()
    code: string;

    @ApiProperty({ description: '<PERSON>ê<PERSON> cấu hình' })
    @Column({ type: 'varchar', length: 255 })
    name: string;

    @ApiProperty({ description: '<PERSON><PERSON> tả cấu hình', required: false })
    @Column({ type: 'text', nullable: true })
    description?: string;

    @Column({ type: 'varchar', length: 20, default: NSConfig.EStatus.ACTIVE })
    status: NSConfig.EStatus;
}