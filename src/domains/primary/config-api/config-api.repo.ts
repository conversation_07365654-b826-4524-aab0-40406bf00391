import { EntityRepository } from "typeorm";
import { PrimaryRepo } from "../primary.repo";

import { ConfigApiEntity } from "./config-api.entity";
import { ConfigApiDetailEntity } from "./config-api-detail.entity";

@EntityRepository(ConfigApiEntity)
export class ConfigApiRepo extends PrimaryRepo<ConfigApiEntity> {}

@EntityRepository(ConfigApiDetailEntity)
export class ConfigApiDetailRepo extends PrimaryRepo<ConfigApiDetailEntity> {}
