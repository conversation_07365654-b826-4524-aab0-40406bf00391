import { ApiProperty } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { Column, Entity, Index } from 'typeorm';
import { NSConfig } from '~/common/enums/config.enum';

@Entity('config_api_detail')
export class ConfigApiDetailEntity extends PrimaryBaseEntity {
  @ApiProperty({ description: 'ID cấu hình' })
  @Column({ type: 'uuid' })
  @Index()
  configApiId: string;

  @ApiProperty({ description: 'Tên field' })
  @Column({ type: 'varchar', length: 100 })
  nameField: string;

  @ApiProperty({ description: 'Key field được mapping' })
  @Column({ type: 'varchar', length: 100 })
  mappingField: string;

  @ApiProperty({ description: `Kiểu dữ liệu ${NSConfig.ETypeField}` })
  @Column({ type: 'varchar', length: 20, default: 'string' })
  type: NSConfig.ETypeField;

  @ApiProperty({ description: 'Bắt buộc', required: false })
  @Column({ type: 'boolean', default: false })
  isRequired: boolean;

  @ApiProperty({ description: 'Ghi chú hoặc mô tả field', required: false })
  @Column({ type: 'text', nullable: true })
  description?: string;
}
