import { Column, Entity, Index } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('package_plan_promotion')
@Index(['packagePlanId', 'billingCycle'], { unique: true })
export class PackagePlanPromotionEntity extends PrimaryBaseEntity {
@ApiProperty({ description: 'ID của gói dịch vụ' })
  @Column({ type: 'uuid' })
  packagePlanId: string;

  @ApiProperty({ description: 'Số tháng thanh toán 1 lần (3/6/9/12)' })
  @Column({ type: 'int' })
  billingCycle: number;

  @ApiProperty({ description: 'Gi<PERSON> bán cho 1 tháng (sau khi giảm)' })
  @Column({ type: 'numeric', precision: 15, scale: 2 })
  unitPrice: number;

  @ApiProperty({ description: '<PERSON><PERSON><PERSON> bán tổng cho kỳ này (sau khi giảm)' })
  @Column({ type: 'numeric', precision: 15, scale: 2 })
  totalPrice: number;

  @ApiPropertyOptional({ description: 'Phần trăm giảm giá (%) so với giá gốc' })
  @Column({ type: 'numeric', precision: 5, scale: 2, nullable: true })
  discountPercent?: number;

  @ApiPropertyOptional({ description: 'Ghi chú khuyến mãi (nếu có)' })
  @Column({ type: 'varchar', nullable: true })
  note?: string;

  @ApiPropertyOptional({ description: 'Stripe Price ID (nếu gắn với Stripe)' })
  @Column({ type: 'varchar', nullable: true })
  stripePriceId?: string;

  @ApiProperty({ description: 'Số lượng giao dịch được phép (tháng)' })
  @Column({ type: 'int' })
  transactionLimit: number;

  @ApiProperty({ description: 'Số lượng cấu hình được phép' })
  @Column({ type: 'int' })
  configLimit: number;

  @ApiProperty({ description: 'Số lượng project (member-key) được phép' })
  @Column({ type: 'int' })
  projectLimit: number;

  @ApiProperty({ description: 'Số lượng byte body size' })
  @Column({ type: 'bigint', nullable: true })
  byteLimit: number;
}
