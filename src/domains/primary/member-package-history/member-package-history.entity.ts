import { Entity, Column, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';
import { NSMember } from '~/common/enums';

// Lịch sử nâng hạ gói
@Entity('member_package_history')
export class MemberPackageHistoryEntity extends PrimaryBaseEntity {
    @ApiProperty({ description: 'ID của thành viên' })
    @Column({ type: 'uuid' })
    @Index()
    memberId: string;

    @ApiProperty({ description: 'ID của gói dịch vụ member đã mua' })
    @Column({ type: 'uuid' })
    @Index()
    memberPackageId: string;

    @ApiProperty({ description: 'ID của gói dịch vụ cũ' })
    @Column({ type: 'uuid' })
    @Index()
    oldPackagePlanId: string;

    @ApiProperty({ description: 'ID của gói dịch vụ mới' })
    @Column({ type: 'uuid' })
    @Index()
    newPackagePlanId: string;

    @ApiProperty({ description: 'Giá gói cũ tại thời điểm thay đổi' })
    @Column({ type: 'numeric', precision: 20, scale: 2, default: 0 })
    oldPrice: number;

    @ApiProperty({ description: 'Giá gói mới tại thời điểm thay đổi' })
    @Column({ type: 'numeric', precision: 20, scale: 2, default: 0 })
    newPrice: number;

    @ApiProperty({ description: 'Ngày hết hạn của gói mới sau khi chuyển' })
    @Column({ type: 'timestamptz', nullable: true })
    newPackageExpiredAt?: Date;

    @ApiProperty({ description: 'Loại thao tác' })
    @Column({ type: 'varchar', length: 20 })
    action: NSMember.EMemberPackageHistoryAction;
}
