import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Column, Entity, Index } from 'typeorm';
import { NSPayment } from '~/common/enums';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('payment_transaction')
export class PaymentTransactionEntity extends PrimaryBaseEntity {
    @ApiProperty({ description: 'ID của member thanh toán (member sở hữu thẻ)' })
    @Column('uuid')
    @Index()
    memberId: string;

    @ApiProperty({ description: 'ID của đơn hàng' })
    @Column('uuid', { nullable: true })
    @Index()
    orderId: string;

    @ApiProperty({ description: 'Đối tác cổng thanh toán' })
    @Column({ nullable: true })
    paymentProvider: NSPayment.EPaymentProvider;

    @ApiProperty({ description: 'Loại giao dịch' })
    @Column({ default: NSPayment.ETransactionType.PAYMENT })
    transactionType: NSPayment.ETransactionType;

    @ApiProperty({ description: 'Trạng thái thanh toán' })
    @Column({ default: NSPayment.ETransactionStatus.PENDING })
    status: NSPayment.ETransactionStatus;

    @ApiProperty({ description: 'Số tiền giao dịch thực tế (bao gồm giảm giá)' })
    @Column('numeric', { precision: 20, scale: 2 })
    amount: number;

    @ApiProperty({ description: 'Tổng số tiền giao dịch (trước khuyến mãi)' })
    @Column('numeric', { precision: 20, scale: 2, default: 0 })
    grossAmount: number;

    @ApiProperty({ description: 'Phương thức thanh toán' })
    @Column({ default: NSPayment.EPaymentMethod.BANK_TRANSFER })
    paymentMethod: NSPayment.EPaymentMethod;

    @ApiProperty({ description: 'Ghi chú đặc biệt' })
    @Column({ nullable: true })
    note?: string;

    //#region data ban dau trùng với data callback
    @ApiProperty({ description: 'Brand name của ngân hàng' })
    @Column({ nullable: true })
    gateway: string;

    @ApiProperty({ description: 'Số tài khoản ngân hàng' })
    @Column({ nullable: true })
    accountNumber: string;
    //#endregion

    //#region data partner payment (Stripe)
    @ApiPropertyOptional({ description: 'ID giao dịch trên cổng thanh toán' })
    @Column({ nullable: true })
    refId?: string; // là paymentIntent.id của Stripe

    @ApiPropertyOptional({ description: 'Client secret của Stripe' })
    @Column({ nullable: true })
    clientSecret?: string;

    @ApiPropertyOptional({ description: 'Số tiền giao dịch partner callback' })
    @Column('numeric', { precision: 20, scale: 0, nullable: true })
    transferAmount?: number;

    @ApiPropertyOptional({ description: 'Nội dung chuyển khoản' })
    @Column({ nullable: true })
    content?: string;

    @ApiPropertyOptional({ description: 'Thời gian xảy ra giao dịch phía ngân hàng' })
    @Column({ type: 'timestamptz', nullable: true })
    transactionDate?: Date;

    @ApiPropertyOptional({ description: 'Số dư tài khoản (lũy kế)' })
    @Column('numeric', { precision: 20, scale: 0, nullable: true })
    accumulated?: number;

    @ApiPropertyOptional({ description: 'Mã tham chiếu của tin nhắn sms' })
    @Column({ nullable: true })
    referenceCode?: string;
    //#endregion

    @ApiPropertyOptional({ description: 'ID của session checkout' })
    @Column({ nullable: true })
    sessionRefId: string;

    @ApiPropertyOptional({ description: 'ID của member package, áp dụng cho tự động gia hạn gói' })
    @Column({ nullable: true })
    memberPackageId: string
}
