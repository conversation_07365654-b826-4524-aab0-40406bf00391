import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response } from 'express';
import { BindRepo } from '~/@core/decorator';
import { LogRequestRepo } from '~/domains/primary/log-request/log-request.repo';

const logEndpoints = [
    '/api/chain/sync-on-chain-transaction',
    '/api/chain/test/sync-on-chain-transaction',
];

const shouldLogRequest = (path: string): boolean => {
    return logEndpoints.some(pattern => {
        if (typeof pattern === 'string') {
            return path.startsWith(pattern);
        }
        return (pattern as RegExp).test(path);
    });
};

@Injectable()
export class RequestLogMiddleware implements NestMiddleware {
    @BindRepo(LogRequestRepo)
    logRequestRepo: LogRequestRepo;

    use(req: Request, res: Response, next: Function) {
        const path = req.path;
        const shouldLog = shouldLogRequest(path);
        const startTime = Date.now();

        if (!shouldLog) {
            return next();
        }

        // Override res.send to capture response body
        const originalSend = res.send.bind(res);
        res.send = (body: any): Response => {
            res.locals._responseBody = body;
            return originalSend(body);
        };

        res.on('finish', () => {
            const responseTime = Date.now() - startTime;

            this.logRequestRepo
                .insert({
                    method: req.method,
                    url: req.originalUrl,
                    headers: (req.headers as any) || {},
                    body: (req.body as any) || {},
                    ip: req.ip,
                    statusCode: res.statusCode,
                    responseTime,
                    response: res.locals._responseBody ?? null,
                })
                .catch(err => {
                    console.error('Error logging request:', err);
                });
        });

        next();
    }
}
