import { NSConfig } from "../enums/config.enum";
import { configEnv } from "~/@config/env";
const { EXTERNAL_API_PATH_PARTNER } = configEnv();

export const ENDPOINT = {
  EXTERNAL_API: [
    {
      code: 'external_api_config_info',
      path: EXTERNAL_API_PATH_PARTNER + '/config/info',
      method: NSConfig.EApiMethod.GET, // API lấy thông tin cấu hình
    },
    // Lấy thông tin dự án
    {
      code: 'external_api_project_info',
      path: EXTERNAL_API_PATH_PARTNER + '/project/info',
      method: NSConfig.EApiMethod.GET, // API lấy thông tin dự án, tham số lấy từ api-key của dự án
    },
    {
      code: 'external_api_config_log',
      path: EXTERNAL_API_PATH_PARTNER + '/config/logs',
      method: NSConfig.EApiMethod.GET, // API lấy log của cấu hình
    },
    {
      code: 'external_api_my_plan',
      path: EXTERNAL_API_PATH_PARTNER + '/my-plan',
      method: NSConfig.EApiMethod.GET, // API lấy thông tin gói dịch vụ của member
    },
    {
      code: 'external_api_payment_transaction',
      path: EXTERNAL_API_PATH_PARTNER + '/payment',
      method: NSConfig.EApiMethod.POST, // API lấy thông tin danh sách giao dịch của member
    },
  ],
};
