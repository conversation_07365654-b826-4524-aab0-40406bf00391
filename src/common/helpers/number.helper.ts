// export const USD_DECIMALS = 30;
const formatAmountToken = ({
  stringNumber,
  tokenDecimals,
}: {
  stringNumber: string;
  tokenDecimals: number;
}) => {
  const numberValue = BigInt(stringNumber);
  const divisor = BigInt('1'.padEnd(tokenDecimals + 1, '0'));
  const scaledNumber = Number(numberValue / divisor);
  return scaledNumber;
  // const formattedNumber = scaledNumber.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

// Tính tiền thuế VAT $
function calculateTotalWithVAT(amount: number, vatRate = 10): number {
  const vat = amount * vatRate / 100;
  const total = amount + vat;

  return parseFloat(total.toFixed(2));
}

function toStripeAmount(amount: number): number {
  return Math.round(amount * 100);
}


export const numberHelper = {
  formatAmountToken,
  calculateTotalWithVAT,
  toStripeAmount,
};
