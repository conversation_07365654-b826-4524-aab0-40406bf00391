import { v4 } from 'uuid';
import { nanoid, customAlphabet } from 'nanoid';

const uuidNoDash = () => {
  return v4().replace(/-/g, '').toUpperCase();
};

const generateReferralCode = () => {
  return customAlphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz')(8);
};

// Function to generate code APE-0000, parameter is number quantity
const generateCode = (prefix: string, length: number) => {
  return `${prefix}-${customAlphabet('0123456789')(length)}`.trim().toUpperCase(); // UpperCase
};

const generateTicketNumber = () => {
  return customAlphabet('0123456789')(6);
};

const slugify = (str: string): string => {
  return str
    .toLowerCase()
    .normalize('NFD')
    .replace(/\p{Diacritic}/gu, '')
    .replace(/[^a-z0-9\s-]/g, '')
    .trim()
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-');
};

const generateSOCode = (quantity?: number, character: string = "SO") => {
  const now = new Date();
  const day = String(now.getDate()).padStart(2, '0');
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const year = String(now.getFullYear()).slice(-2);

  const formattedSequence = String(quantity).padStart(6, '0');
  const soCode = `${character}${day}${month}${year}${formattedSequence}`;
  return soCode;
}

export function generateOrderCode(): string {
  const date = new Date().toISOString().slice(0, 10).replace(/-/g, '')
  const id = nanoid(10)
  return `ORD-${date}-${id.toUpperCase()}`
}

export const generateCodeHelper = {
  uuidNoDash,
  generateReferralCode,
  generateTicketNumber,
  generateCode,
  slugify,
  generateOrderCode,
  generateSOCode,
};
