import dayjs from 'dayjs';

// Viết hàm với tham số là từ 0 -> 6 trả về thứ trong tuần
const getDayOfWeek = (day: number, locale: string = 'vi') => {
  const days = {
    vi: ['<PERSON><PERSON> nhật', 'Th<PERSON> hai', '<PERSON>h<PERSON> ba', '<PERSON><PERSON><PERSON> tư', '<PERSON>h<PERSON> năm', '<PERSON><PERSON><PERSON> sáu', '<PERSON>h<PERSON> bảy'],
    en: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
  };
  return days[locale][day];
};

// Viết hàm với tham số là từ 0 -> 11 trả về tháng trong năm
const getMonthOfYear = (month: number, locale: string = 'vi') => {
  const months = {
    vi: [
      'Tháng 1',
      'Tháng 2',
      'Tháng 3',
      'Tháng 4',
      'Tháng 5',
      'Tháng 6',
      'Tháng 7',
      'Tháng 8',
      'Tháng 9',
      'Tháng 10',
      'Tháng 11',
      'Tháng 12',
    ],
    en: [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ],
  };
  return months[locale][month];
};

export const dateHelper = {
  getDayOfWeek,
  getMonthOfYear,
};
