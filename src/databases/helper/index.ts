import { TypeOrmModule } from '@nestjs/typeorm';
import { stringToBoolean } from '~/utils/parser.util';

export class DatabaseConfig {
  type: string;
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  synchronize: boolean;
  ssl: boolean;
  sslRejectUnauthorized: boolean;
  entities: string[];
}

export const initConnection = ({
  name = 'default',
  type,
  host,
  port,
  username,
  password,
  database,
  synchronize,
  ssl,
  sslRejectUnauthorized,
  entities = [],
}) => {
  if (!type) {
    throw new Error('TYPE is required');
  }
  if (type === 'postgres') {
    /**
     * Config database Postgres
     */
    return TypeOrmModule.forRoot({
      name,
      type: 'postgres',
      host,
      port: Number(port),
      username,
      password,
      database,
      synchronize: stringToBoolean(synchronize),
      ssl: stringToBoolean(ssl)
        ? { rejectUnauthorized: stringToBoolean(sslRejectUnauthorized) }
        : undefined,
      entities,
      logging: [
        'log',
        'error',
        'info',
        // "query"
      ],
      // logger: 'simple-console',
    });
  }
  if (type === 'mssql') {
    /**
     * Config database MSSQL
     */
    return TypeOrmModule.forRoot({
      name,
      type: 'mssql',
      host,
      port: Number(port),
      username,
      password,
      database,
      synchronize: stringToBoolean(synchronize),
      options: {
        encrypt: false,
      },
      entities,
      logging: [
        'log',
        'error',
        'info',
        // "query"
      ],
      // logger: 'simple-console',
    });
  }
  if (type === 'mysql') {
    /**
     * Config database MySQL
     */
    return TypeOrmModule.forRoot({
      name,
      type: 'mysql',
      host,
      port: Number(port),
      username,
      password,
      database,
      synchronize: stringToBoolean(synchronize),
      entities,
      logging: [
        'log',
        'error',
        'info',
        // "query"
      ],
      // logger: 'simple-console',
    });
  }
};
