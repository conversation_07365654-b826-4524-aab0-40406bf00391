import { MiddlewareConsumer, Module, NestModule, RequestMethod } from '@nestjs/common';
import { configEnv } from './@config/env';
import * as allModules from './modules';
import { ContextMiddleware, LoggerMiddleware, RequestLogMiddleware } from './@systems/middlewares';
import { JwtModule } from '@nestjs/jwt';
import { ScheduleModule } from '@nestjs/schedule';
import { CacheModule } from '@nestjs/cache-manager';
import { AcceptLanguageResolver, HeaderResolver, I18nModule, QueryResolver } from 'nestjs-i18n';
import { join } from 'path';
import { databases } from './databases';
import { ThrottlerModule } from '@nestjs/throttler';
import { APP_GUARD } from '@nestjs/core';
import { ThrottlerGuard } from '@nestjs/throttler';
import { RedisModule } from './modules/@global';

const envConfig = configEnv();

const { JWT_EXPIRY, JWT_SECRET } = configEnv();

const globalModules = [
    JwtModule.register({
        global: true,
        secret: JWT_SECRET,
        signOptions: { expiresIn: JWT_EXPIRY, algorithm: 'HS256' },
    }),
    ScheduleModule.forRoot(),
    CacheModule.register({
        isGlobal: true,
        store: 'memory',
    }),
    I18nModule.forRootAsync({
        useFactory: () => ({
            fallbackLanguage: 'vi',
            loaderOptions: {
                path: join(__dirname, '/assets/locales/'),
                watch: true,
            },
            typesOutputPath: join(__dirname, '../src/assets/i18n.generated.ts'),
        }),
        resolvers: [
            new HeaderResolver(['x-lang']),
            { use: QueryResolver, options: ['lang'] },
            AcceptLanguageResolver,
        ],
    }),
    ThrottlerModule.forRoot({
        throttlers: [
            {
                ttl: 60000, // 60 seconds in milliseconds
                limit: 200,
            },
        ],
    }),
    RedisModule.forRoot({
        username: 'default',
        password: '1ClJpulXKa7xdL5KXrlwoVTOnAtCpWbc',
        socket: {
            host: 'redis-12899.c244.us-east-1-2.ec2.redns.redis-cloud.com',
            port: 12899,
        },
    }),
];
const modules = Object.values(allModules);
@Module({
    imports: [...databases, ...globalModules, ...modules],
    providers: [
        {
            provide: APP_GUARD,
            useClass: ThrottlerGuard,
        },
    ],
})
export class AppModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {
        consumer
            .apply(ContextMiddleware, LoggerMiddleware)
            .forRoutes({ path: '*', method: RequestMethod.ALL });
        consumer.apply(RequestLogMiddleware).forRoutes({ path: '*', method: RequestMethod.ALL });
    }
}
